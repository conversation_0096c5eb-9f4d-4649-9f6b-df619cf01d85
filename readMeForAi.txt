APPLICATION LAYER CLASSES - TURNERA CODEBASE

This document lists all Java classes found in application layer packages organized by their specific submodule packages with full import paths:

=== APPOINTMENT MODULE ===

== appointment.application.find ==
com.turnera.turnera.appointment.application.find.FindAppointmentById : Finds appointments by ID, returning Optional for safe null handling
com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndConsultationTypeForDate : Retrieves appointments for specific medical center, consultation type, and date
com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek : Finds appointments by medical center, professional, and day of week
com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForDate : Retrieves appointments for specific medical center, professional, and date
com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForMonth : Finds monthly appointments for medical center and professional combinations
com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterInMonthAndYear : Retrieves all appointments for a medical center within a specific month
com.turnera.turnera.appointment.application.find.FindFutureAppointmentsForMedicalCenterAndProfessional : Finds upcoming appointments for medical center and professional combinations

== appointment.application.make ==
com.turnera.turnera.appointment.application.make.MakeAppointment : Creates new appointments with comprehensive validation including professional relationships and schedule conflicts

== appointment.application.migrate ==
com.turnera.turnera.appointment.application.migrate.MigrateAppointments : Migrates appointment data from old patient IDs to new patient IDs during patient consolidation

== appointment.application.modify ==
com.turnera.turnera.appointment.application.modify.ModifyAppointment : Updates existing appointment details including professional, consultation types, date, time, and price

== appointment.application.validations ==
com.turnera.turnera.appointment.application.validations.AppointmentValidations : Validates appointment existence and retrieves appointments by ID for business operations

=== BLOCKED SLOT MODULE ===

== blockedSlot.application.create ==
com.turnera.turnera.blockedSlot.application.create.CreateBlockedSlot : Creates blocked time slots for professionals, validating against existing appointments and schedules

== blockedSlot.application.find ==
com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsByProfessionalAndMedicalCenterAndDate : Finds blocked slots for professional-medical center combinations on specific dates
com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsForMedicalCenterAndProfessionalForMonth : Retrieves monthly blocked slots for professional-medical center pairs
com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsForMedicalCenterInMonthAndYear : Finds all blocked slots for a medical center within a specific month

=== CONSULTATION TYPE MODULE ===

== consultationType.application.find ==
com.turnera.turnera.consultationType.application.find.FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter : Retrieves consultation type relationships for professional-medical center pairs

== consultationType.application.validations ==
com.turnera.turnera.consultationType.application.validations.ConsultationTypeValidations : Validates consultation type relationships, patient limits, and cooldown periods for appointments

=== EMPLOYEE USER MODULE ===

== employeeUser.application.find ==
com.turnera.turnera.employeeUser.application.find.FindEmployeeUserByAuth0Id : Finds employee users by their Auth0 authentication ID
com.turnera.turnera.employeeUser.application.find.FindEmployeeUserById : Retrieves employee users by their internal ID
com.turnera.turnera.employeeUser.application.find.FindMaybeEmployeeUserByAuth0Id : Safely finds employee users by Auth0 ID, returning Optional
com.turnera.turnera.employeeUser.application.find.GetEmployeeUserMedicalCenters : Retrieves all medical centers associated with a specific employee user

== employeeUser.application.validations ==
com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations : Validates employee user existence and permissions for medical center operations

=== HEALTH INSURANCE MODULE ===

== healthInsurance.application.associate ==
com.turnera.turnera.healthInsurance.application.associate.AssociateHealthInsurancesToMedicalCenter : Associates multiple health insurances to a medical center through employee user requests
com.turnera.turnera.healthInsurance.application.associate.AssociateHealthInsurancesToProfessionalInMedicalCenter : Associates health insurances to professionals within specific medical centers

== healthInsurance.application.find ==
com.turnera.turnera.healthInsurance.application.find.FindHealthInsurance : Finds health insurance entities by their ID
com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesByProvider : Finds health insurance entities by provider name or identifier
com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesForProfessionalInMedicalCenter : Retrieves health insurances associated with a professional in a specific medical center
com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesInMedicalCenter : Retrieves all health insurances associated with a specific medical center

== healthInsurance.application.validations ==
com.turnera.turnera.healthInsurance.application.validations.HealthInsuranceValidations : Validates health insurance existence and relationships

=== MEDICAL CENTER MODULE ===

== medicalCenter.application.create ==
com.turnera.turnera.medicalCenter.application.create.CreateMedicalCenter : Creates new medical center entities with provided input data

== medicalCenter.application.find ==
com.turnera.turnera.medicalCenter.application.find.FindGeneralInformation : Retrieves general information about medical centers including address, contact, and schedule details
com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterById : Retrieves medical center entities by their ID
com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterPatients : Finds all patients associated with a specific medical center with appointment history
com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterProfessionalInformation : Retrieves comprehensive professional information for a medical center including agendas and specialties
com.turnera.turnera.medicalCenter.application.find.FindMultipleProfessionalAgendas : Retrieves agenda information for multiple professionals within a medical center for a specific month
com.turnera.turnera.medicalCenter.application.find.FindProfessionalAgenda : Retrieves detailed agenda information for a specific professional in a medical center

== medicalCenter.application.sse ==
com.turnera.turnera.medicalCenter.application.sse.GetAllMedicalCenterInformationForSse : Aggregates all medical center information (patients and professionals) for SSE broadcasting

== medicalCenter.application.sse.doctor ==
com.turnera.turnera.medicalCenter.application.sse.doctor.GetProfessionalDtoForSse : Generates SSE events for professional data in a medical center

== medicalCenter.application.sse.patients ==
com.turnera.turnera.medicalCenter.application.sse.patients.GetPatientDtoForSse : Generates SSE events for individual patient data in a medical center
com.turnera.turnera.medicalCenter.application.sse.patients.GetPatientsDtoForSse : Generates SSE events for all patients data in a medical center

== medicalCenter.application.validations ==
com.turnera.turnera.medicalCenter.application.validations.MedicalCenterValidations : Validates medical center existence for business operations

=== PATIENT MODULE ===

== patient.application.associate ==
com.turnera.turnera.patient.application.associate.AssociateHealthInsuranceToPatient : Associates health insurance to patient entities with user tracking and creator type
com.turnera.turnera.patient.application.associate.AssociateMedicalCenter : Associates medical centers to patients with user tracking and creator type information

== patient.application.create ==
com.turnera.turnera.patient.application.create.CreatePatient : Creates new patient entities and associates them with medical centers through employee users
com.turnera.turnera.patient.application.create.CreatePatientForUser : Creates patient entities for turnera users and migrates existing appointment data

== patient.application.find ==
com.turnera.turnera.patient.application.find.FindPatientById : Finds patient entities by their ID from the domain service
com.turnera.turnera.patient.application.find.FindPatientByUserIdAndIdentificationNumber : Finds patient entities by user ID and identification number combination

== patient.application.update ==
com.turnera.turnera.patient.application.update.UpdateMedicalCenterRelationshipByAppointment : Updates patient-medical center relationships based on appointment data

== patient.application.validations ==
com.turnera.turnera.patient.application.validations.PatientValidations : Validates patient existence and medical center relationships

=== PROFESSIONAL MODULE ===

== professional.application.create ==
com.turnera.turnera.professional.application.create.CreateProfessionalMedicalCenterRelationship : Creates relationships between professionals and medical centers with health insurance associations

== professional.application.details ==
com.turnera.turnera.professional.application.details.ProfessionalDetailService : Provides detailed professional information including consultation types and schedule data

== professional.application.find ==
com.turnera.turnera.professional.application.find.FindMaybeProfessionalByAuth0Id : Safely retrieves professionals by Auth0 ID, returning Optional for null safety
com.turnera.turnera.professional.application.find.FindProfessionalById : Finds professional entities by their ID
com.turnera.turnera.professional.application.find.FindProfessionalByMedicalLicense : Retrieves professionals by their medical license number
com.turnera.turnera.professional.application.find.FindProfessionalMedicalCenterRelationshipById : Finds professional-medical center relationship entities by ID

== professional.application.validations ==
com.turnera.turnera.professional.application.validations.ProfessionalValidations : Validates professional existence, medical center relationships, and professional-specific business rules

=== SCHEDULE MODULE ===

== schedule.application.create ==
com.turnera.turnera.schedule.application.create.CreateAppointmentSchedules : Creates multiple appointment schedule entities in batch operations
com.turnera.turnera.schedule.application.create.CreateMedicalCenterSchedule : Creates medical center schedule entities for specific days of the week
com.turnera.turnera.schedule.application.create.SetAppointmentSchedule : Orchestrates appointment schedule creation, modification, and deletion for specific days of the week
com.turnera.turnera.schedule.application.create.SetMedicalCenterSchedule : Orchestrates medical center schedule creation, modification, and deletion operations
com.turnera.turnera.schedule.application.create.SetSpecialSchedule : Creates special schedule entries with collision validation against existing schedules
com.turnera.turnera.schedule.application.create.SetVacationSchedule : Creates vacation schedules for professionals after validating against existing appointments

== schedule.application.delete ==
com.turnera.turnera.schedule.application.delete.DeleteAppointmentSchedules : Deletes multiple appointment schedule entities in batch operations
com.turnera.turnera.schedule.application.delete.DeleteMedicalCenterSchedules : Deletes multiple medical center schedule entities in batch operations
com.turnera.turnera.schedule.application.delete.DeleteSpecialSchedule : Deletes special schedule entries after validating no conflicting appointments exist
com.turnera.turnera.schedule.application.delete.DeleteVacationSchedule : Deletes vacation schedule entries with medical center ownership validation

== schedule.application.find ==
com.turnera.turnera.schedule.application.find.FindScheduleInfoForProfessionalAndMedicalCenter : Retrieves schedule information for professional-medical center combinations

== schedule.application.modify ==
com.turnera.turnera.schedule.application.modify.ModifyAppointmentSchedules : Updates multiple appointment schedule entities with new time and configuration data
com.turnera.turnera.schedule.application.modify.ModifyMedicalCenterSchedule : Updates medical center schedule entities with new day and time information

== schedule.application.validations ==
com.turnera.turnera.schedule.application.validations.ValidateAgendaForSlotCreation : Validates appointment slot creation against existing schedules, appointments, and blocked slots

=== SPECIALTY MODULE ===

== specialty.application.find ==
com.turnera.turnera.specialty.application.find.FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter : Retrieves specialty relationships for professional-medical center combinations

=== SSE MODULE ===

== sse.application.medicalCenter ==
com.turnera.turnera.sse.application.medicalCenter.SseMedicalCenterEvents : Manages Server-Sent Events for medical center real-time data broadcasting with heartbeat and connection management

=== TURNERA USER MODULE ===

== turneraUser.application.find ==
com.turnera.turnera.turneraUser.application.find.FindActiveTurneraUserById : Retrieves active turnera users by their ID from the domain service
com.turnera.turnera.turneraUser.application.find.FindMaybeTurneraUserByAuth0Id : Safely finds turnera users by Auth0 ID, returning Optional

== turneraUser.application.validations ==
com.turnera.turnera.turneraUser.application.validations.UserValidations : Validates turnera user existence and active status

=== USER MODULE ===

== user.application ==
com.turnera.turnera.user.application.FindUserInformationByAuth0Id : Aggregates user information from multiple user types (turnera, employee, professional) by Auth0 ID