CREATE TABLE professional
(
    id                    SERIAL PRIMARY KEY,
    external_id           UUID DEFAULT gen_random_uuid(),
    name                  <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    surname               VARCHAR(255) NOT NULL,
    identification_number VA<PERSON>HAR(255),
    medical_license       VARCHAR(255) NOT NULL,
    created_by            INTEGER      NOT NULL,
    created_at            TIMESTAMP    NOT NULL,
    auth0_id              VARCHAR(255),
    constraint unique_professional unique (medical_license)
);

alter table professional
    add constraint unique_professional unique (medical_license);

alter table professional
    add column auth0_id VARCHAR(255);

