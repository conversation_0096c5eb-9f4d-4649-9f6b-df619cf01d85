CREATE TYPE week_day AS ENUM (
    'MONDAY',
    'TUESDAY',
    'WEDNESDAY',
    'THURSDAY',
    'FRIDAY',
    'SATURDAY',
    'SUNDAY');
CREATE CAST (character varying AS week_day) WITH INOUT AS ASSIGNMENT;
CREATE TABLE appointment_schedule
(
    id                SERIAL PRIMARY KEY,
    medical_center_id INTEGER   NOT NULL REFERENCES medical_center (id),
    professional_id   INTEGER   NOT NULL REFERENCES professional (id),
    starting_at       DATE      NOT NULL,
    ending_at         DATE,
    start_time        TIME      NOT NULL,
    end_time          TIME      NOT NULL,
    day               week_day  NOT NULL,
    created_by        INTEGER   NOT NULL references employee_user (id),
    created_at        TIMESTAMP NOT NULL
);


alter table appointment_schedule
    add foreign key (professional_id, medical_center_id)
        references professional_medical_center_relationship (professional_id, medical_center_id);

alter table appointment_schedule
    drop column appointment_interval_time;