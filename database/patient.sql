CREATE TABLE patient
(
    id                    SERIAL PRIMARY KEY,
    user_id               INTEGER references turnera_user (id),
    name                  <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    surname               VA<PERSON>HAR(255) NOT NULL,
    identification_number VARCHAR(255) NOT NULL,
    phone                 VARCHAR(255) NOT NULL,
    date_of_birth         DATE         NOT NULL,
    email                 VARCHAR(255),
    attendance_percentage DECIMAL,
    created_by            INTEGER,
    created_at            TIMESTAMP,
    updated_by            INTEGER,
    updated_at            TIMESTAMP
);

alter table patient
    add column date_of_birth DATE NOT NULL;

alter table patient
    add column health_insurance_id INTEGER references health_insurance (id);
