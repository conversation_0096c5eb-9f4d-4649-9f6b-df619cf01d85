CREATE TABLE medical_center_schedule
(
    id                SERIAL PRIMARY KEY,
    medical_center_id INTEGER   NOT NULL REFERENCES medical_center (id),
    start_time        TIME      NOT NULL,
    end_time          TIME      NOT NULL,
    day               week_day  NOT NULL,
    created_by        INTEGER   NOT NULL references employee_user (id),
    created_at        TIMESTAMP NOT NULL,
    updated_by        INTEGER   NOT NULL references employee_user (id),
    updated_at        TIMESTAMP NOT NULL,
    constraint unique_medical_center_schedule
        unique (medical_center_id, day)
);


