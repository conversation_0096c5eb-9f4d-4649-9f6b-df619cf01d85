CREATE TYPE employee_user_type AS ENUM ('ADMIN', 'RECEPTIONIST','SUPER_ADMIN');
CREATE CAST (character varying AS employee_user_type) WITH INOUT AS ASSIGNMENT;
alter type employee_user_type add value if not exists 'SUPER_ADMIN';


CREATE TABLE employee_user_medical_center_relationship
(
    employee_user_id  INTEGER REFERENCES employee_user (id),
    medical_center_id INTEGER REFERENCES medical_center (id),
    type              employee_user_type NOT NULL
);
