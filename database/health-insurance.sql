CREATE TABLE health_insurance
(
    id                    SERIAL PRIMARY KEY,
    health_insurance      VARCHAR(255) NOT NULL,
    health_insurance_plan VARCHAR(255) NOT NULL
);


alter table health_insurance
    add constraint unique_health_insurance_plan unique (health_insurance, health_insurance_plan);



INSERT INTO health_insurance (health_insurance, health_insurance_plan)
VALUES
-- OSDE
('OSDE', '310'),
('OSDE', '410'),
('OSDE', '510'),

-- Swiss Medical
('Swiss Medical', 'S1'),
('Swiss Medical', 'S2'),
('Swiss Medical', 'SMG02'),
('Swiss Medical', 'SMG20'),
('Swiss Medical', 'SMG30'),
('Swiss Medical', 'SMG40'),
('Swiss Medical', 'SMG50'),
('Swiss Medical', 'SMG60'),
('Swiss Medical', 'SMG70'),

-- Medif<PERSON>
('Medifé', 'Medifé +'),
('Medifé', 'Bronce Classic'),
('Medifé', '<PERSON>ronce'),
('Medif<PERSON>', '<PERSON>'),
('Medif<PERSON>', '<PERSON>'),
('Medif<PERSON>', 'Platinum'),

-- <PERSON><PERSON>
('Galeno', '<PERSON>'),
('<PERSON><PERSON>', '<PERSON>'),
('<PERSON>o', 'Azul'),
('<PERSON>o', 'Plata 220'),
('Galeno', 'Oro 330'),
('Galeno', 'Azul 440'),
('Galeno', '<PERSON> 550'),

-- Omint
('Omint', 'Classic'),
('Omint', 'Premium'),
('Omint', 'Excellence'),

-- Accord Salud
('Accord Salud', 'Plan 1'),
('Accord Salud', 'Plan 2'),
('Accord Salud', 'Plan 3'),

-- Medicus
('Medicus', 'Plan 1'),
('Medicus', 'Plan 2'),
('Medicus', 'Plan 3');