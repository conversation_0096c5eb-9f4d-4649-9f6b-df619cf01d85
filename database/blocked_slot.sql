CREATE TYPE blocked_slot_type AS ENUM ('DOCTOR', 'PATIENT');
CREATE CAST (character varying AS blocked_slot_type) WITH INOUT AS ASSIGNMENT;

CREATE TYPE blocked_slot_status AS ENUM ('ACTIVE', 'EXPIRED', 'CANCELLED');
CREATE CAST (character varying AS blocked_slot_status) WITH INOUT AS ASSIGNMENT;

drop cast (character varying AS blocked_slot_status);
drop type blocked_slot_status;


CREATE TABLE blocked_slot
(
    id                          SERIAL PRIMARY KEY,
    date                        DATE              NOT NULL,
    start_time                  TIME              NOT NULL,
    appointment_interval_amount INTEGER           NOT NULL,
    slot_type                   blocked_slot_type NOT NULL,
    professional_id             BIGINT            NOT NULL REFERENCES professional (id),
    patient_id                  INTEGER REFERENCES patient (id),
    medical_center_id           INTEGER           NOT NULL REFERENCES medical_center (id),
    created_at                  TIMESTAMP,
    updated_at                  TIMESTAMP,
    creator_type                USER_TYPE         NOT NULL,
    created_by                  INTEGE<PERSON>,
    updater_type                USER_TYPE         NOT NULL,
    updated_by                  INTEGER,
    CONSTRAINT check_slot_type_references CHECK (
        (slot_type = 'DOCTOR' AND professional_id IS NOT NULL AND patient_id IS NULL) OR
        (slot_type = 'PATIENT' AND patient_id IS NOT NULL)
        )
);

CREATE INDEX blocked_slot_date_idx ON blocked_slot (date);
CREATE INDEX blocked_slot_professional_idx ON blocked_slot (professional_id) WHERE professional_id IS NOT NULL;
CREATE INDEX blocked_slot_patient_idx ON blocked_slot (patient_id) WHERE patient_id IS NOT NULL;
CREATE INDEX blocked_slot_medical_center_idx ON blocked_slot (medical_center_id);



