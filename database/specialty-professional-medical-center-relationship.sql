CREATE TABLE specialty_professional_medical_center_relationship
(
    specialty_id      INTEGER   NOT NULL REFERENCES specialty (id),
    professional_id   INTEGER   NOT NULL REFERENCES professional (id),
    medical_center_id INTEGER   NOT NULL REFERENCES medical_center (id),
    created_by        INTEGER   NOT NULL references employee_user (id),
    created_at        TIMESTAMP NOT NULL,
    PRIMARY KEY (specialty_id, professional_id, medical_center_id)
);

alter table specialty_professional_medical_center_relationship
    add foreign key (professional_id, medical_center_id)
        references professional_medical_center_relationship (professional_id, medical_center_id);