CREATE TABLE insurance_consultation_professional_medical_center_relationship
(
    health_insurance_id  INTEGER   NOT NULL REFERENCES health_insurance (id),
    consultation_type_id INTEGER   NOT NULL REFERENCES consultation_type (id),
    professional_id      INTEGER   NOT NULL REFERENCES professional (id),
    medical_center_id    INTEGER   NOT NULL REFERENCES medical_center (id),
    co_payment_price     DECIMAL,
    is_excluded          BOOLEAN   NOT NULL,
    created_by           INTEGER   NOT NULL references employee_user (id),
    created_at           TIMESTAMP NOT NULL,
    updated_by           INTEGER   NOT NULL references employee_user (id),
    updated_at           TIMESTAMP NOT NULL,
    PRIMARY KEY (health_insurance_id, consultation_type_id, professional_id, medical_center_id)
);

alter table insurance_consultation_professional_medical_center_relationship
    add foreign key (professional_id, medical_center_id)
        references professional_medical_center_relationship (professional_id, medical_center_id);


alter table insurance_consultation_professional_medical_center_relationship
    add foreign key (consultation_type_id, professional_id, medical_center_id)
        references consultation_type_professional_medical_center_relationship (consultation_type_id, professional_id, medical_center_id);