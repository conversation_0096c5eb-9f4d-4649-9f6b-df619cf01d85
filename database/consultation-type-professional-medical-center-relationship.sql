CREATE TABLE consultation_type_professional_medical_center_relationship
(
    consultation_type_id        INTEGER   NOT NULL REFERENCES consultation_type (id),
    professional_id             INTEGER   NOT NULL REFERENCES professional (id),
    medical_center_id           INTEGER   NOT NULL REFERENCES medical_center (id),
    price                       DECIMAL,
    available_online            BOOLEAN   NOT NULL,
    accepts_self_paid_patient   BOOLEAN   NOT NULL,
    requires_medical_order      BOOLEAN   NOT NULL,
    appointment_interval_amount INTEGER   NOT NULL,
    daily_limit                 INTEGER,
    created_by                  INTEGER   NOT NULL references employee_user (id),
    created_at                  TIMESTAMP NOT NULL,
    updated_by                  INTEGER   NOT NULL references employee_user (id),
    updated_at                  TIMESTAMP NOT NULL,
    PRIMARY KEY (consultation_type_id, professional_id, medical_center_id)
);

alter table consultation_type_professional_medical_center_relationship
    add foreign key (professional_id, medical_center_id)
        references professional_medical_center_relationship (professional_id, medical_center_id);



alter table consultation_type_professional_medical_center_relationship
    add column instructions VARCHAR;