CREATE TYPE appointment_status AS ENUM (
    'CANCELLED',
    'PENDING',
    'NO_SHOW',
    'IN_WAITING_ROOM',
    'IN_CONSULTATION',
    'COMPLETE',
    'PROCESSING',
    'SETTLED');
CREATE CAST (character varying AS appointment_status) WITH INOUT AS ASSIGNMENT;
CREATE TYPE appointment_source AS ENUM ('TURNERA', 'PHONE', 'IN_PERSON');
CREATE CAST (character varying AS appointment_source) WITH INOUT AS ASSIGNMENT;

CREATE TABLE appointment
(
    id                          SERIAL PRIMARY KEY,
    date                        DATE               NOT NULL,
    start_time                  TIME               NOT NULL,
    appointment_interval_amount INTEGER            NOT NULL,
    patient_notes               VARCHAR,
    patient_id                  INTEGER            NOT NULL REFERENCES patient (id),
    professional_id             BIGINT             NOT NULL REFERENCES professional (id),
    medical_center_id           INTEGER            NOT NULL REFERENCES medical_center (id),
    health_insurance_id         INTEGER REFERENCES health_insurance (id),
    price                       DECIMAL,
    status                      appointment_status NOT NULL,
    last_status_update          TIMESTAMP,
    source                      appointment_source,
    creator_type                USER_TYPE          NOT NULl,
    created_by                  INTEGER,
    created_at                  TIMESTAMP,
    updater_type                USER_TYPE          NOT NULL,
    updated_by                  INTEGER,
    updated_at                  TIMESTAMP
);


alter table appointment
    add foreign key (patient_id, medical_center_id)
        references patient_medical_center_relationship (patient_id, medical_center_id);
ALTER TABLE appointment
    ALTER COLUMN professional_id TYPE BIGINT;