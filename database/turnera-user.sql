CREATE TYPE USER_STATE AS ENUM ('UNVERIFIED', 'E<PERSON>IL_VERIFIED', 'ACTIVE', 'INACTIVE');
CREATE CAST (character varying AS user_state) WITH INOUT AS ASSIGNMENT;

DROP CAST (character varying AS user_state);
CREATE TABLE turnera_user
(
    id         SERIAL PRIMARY KEY,
    name       <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    surname    VA<PERSON>HA<PERSON>(100) NOT NULL,
    email      VARCHAR(100) UNIQUE,
    phone      VARCHAR(20),
    state      USER_STATE   NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    auth0_id   VARCHAR(255) NOT NULL,
    CONSTRAINT email_verified_or_active CHECK (
        (state = 'EMAIL_VERIFIED' AND email IS NOT NULL)
            OR (state = 'ACTIVE' AND email IS NOT NULL AND phone IS NOT NULL)
            OR state = 'UNVERIFIED'
        )
);

