CREATE TYPE overlapped_appointment_limit AS ENUM (
    'ONE_PER_ONE',
    'ONE_PER_TWO',
    'ONE_PER_THREE',
    'TWO_PER_ONE',
    'NONE');
CREATE CAST (character varying AS overlapped_appointment_limit) WITH INOUT AS ASSIGNMENT;

CREATE TABLE professional_medical_center_relationship
(
    professional_id                             BIGINT                         NOT NULL REFERENCES professional (id),
    medical_center_id                           INTEGER                        NOT NULL REFERENCES medical_center (id),
    overlapped_appointment_limit                overlapped_appointment_limit,
    maximum_anticipation_appointment_time_limit INTERVAL,
    minimum_anticipation_appointment_time_limit INTERVAL,
    email                                       VARCHAR,
    appointment_interval_time                   TIME                           NOT NULL,
    external_id                                 UUID DEFAULT gen_random_uuid() NOT NULL,
    created_by                                  INTEGER                        NOT NULL references employee_user (id),
    created_at                                  TIMESTAMP                      NOT NULL,
    PRIMARY KEY (professional_id, medical_center_id)
);


ALTER TABLE professional_medical_center_relationship
    ALTER COLUMN professional_id TYPE BIGINT;

ALTER table professional_medical_center_relationship
    add column appointment_interval_time TIME NOT NULL DEFAULT '00:15:00';
