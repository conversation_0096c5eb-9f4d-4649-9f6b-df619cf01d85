CREATE TYPE request_state AS ENUM ('UNSIGNED', 'SIGNED', 'CANCELLED','EXPIRED');
CREATE CAST (character varying AS request_state) WITH INOUT AS ASSIGNMENT;

CREATE TABLE request
(
    id         SERIAL PRIMARY KEY,
    user_id    INTEGER NOT NULL references turnera_user (id),
    token      VARCHAR(255),
    state      request_state,
    created_by INTEGER,
    created_at TIMESTAMP,
    updated_by INTEGER,
    updated_at TIMESTAMP
);
