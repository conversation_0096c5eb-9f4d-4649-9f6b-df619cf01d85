CREATE TABLE special_schedule
(
    id                SERIAL PRIMARY KEY,
    medical_center_id INTEGER   NOT NULL REFERENCES medical_center (id),
    professional_id   INTEGER   NOT NULL REFERENCES professional (id),
    date              DATE      NOT NULL,
    start_time        TIME      NOT NULL,
    end_time          TIME      NOT NULL,
    created_by        INTEGER   NOT NULL references employee_user (id),
    created_at        TIMESTAMP NOT NULL
);


alter table special_schedule
    add foreign key (professional_id, medical_center_id)
        references professional_medical_center_relationship (professional_id, medical_center_id);
