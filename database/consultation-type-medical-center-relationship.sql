CREATE TABLE consultation_type_medical_center_relationship
(
    consultation_type_id INTEGER   NOT NULL REFERENCES consultation_type (id),
    medical_center_id    INTEGER   NOT NULL REFERENCES medical_center (id),
    patient_limit        INTEGER,
    cooldown_period      TIME,
    created_by           INTEGER   NOT NULL references employee_user (id),
    created_at           TIMESTAMP NOT NULL,
    updated_by           INTEGER   NOT NULL references employee_user (id),
    updated_at           TIMESTAMP NOT NULL,
    PRIMARY KEY (consultation_type_id, medical_center_id)
);