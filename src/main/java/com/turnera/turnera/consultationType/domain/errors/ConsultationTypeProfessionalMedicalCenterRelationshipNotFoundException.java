package com.turnera.turnera.consultationType.domain.errors;

import com.turnera.turnera.configuration.exceptions.NotFoundException;

public class ConsultationTypeProfessionalMedicalCenterRelationshipNotFoundException extends NotFoundException {
    public ConsultationTypeProfessionalMedicalCenterRelationshipNotFoundException(Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        super("Consultation type relationship not found for consultation type ID: " + consultationTypeId + ", medical center ID: " + medicalCenterId + ", professional ID: " + professionalId);
    }
}