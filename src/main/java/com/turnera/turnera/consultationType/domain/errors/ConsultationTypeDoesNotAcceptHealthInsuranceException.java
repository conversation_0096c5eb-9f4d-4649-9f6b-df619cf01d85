package com.turnera.turnera.consultationType.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class ConsultationTypeDoesNotAcceptHealthInsuranceException extends BadRequestException {
    public ConsultationTypeDoesNotAcceptHealthInsuranceException(Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        super("ConsultationType: " + consultationTypeId + " for professional: " + professionalId + " and medical center: " + medicalCenterId + " does not accept health insurance");
    }
}