package com.turnera.turnera.consultationType.domain;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeMedicalCenterRelationship;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import java.util.List;
import java.util.Optional;

public interface ConsultationTypeService {

  List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findProfessionalMedicalCenterRelationshipByConsultationIdsInMedicalCenterIdAndProfessionalId(
          List<Integer> consultationTypeId, Integer medicalCenterId, Integer professionalId);

  Optional<ConsultationTypeMedicalCenterRelationship>
      findMaybeMedicalCenterRelationshipByIdAndMedicalCenterId(
          Integer consultationTypeId, Integer medicalCenterId);

  List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter(
          Integer professionalId, Integer medicalCenterId);
}
