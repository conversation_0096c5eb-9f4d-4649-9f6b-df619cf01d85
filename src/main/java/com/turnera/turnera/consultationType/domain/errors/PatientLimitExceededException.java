package com.turnera.turnera.consultationType.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class PatientLimitExceededException extends BadRequestException {
    public PatientLimitExceededException(Integer consultationTypeId, Integer medicalCenterId) {
        super("Appointment does not respect the relationship patient limit for consultation type: %d and medical center: %d".formatted(consultationTypeId, medicalCenterId));
    }
}