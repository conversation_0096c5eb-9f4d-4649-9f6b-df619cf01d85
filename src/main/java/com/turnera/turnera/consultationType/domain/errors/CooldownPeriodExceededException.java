package com.turnera.turnera.consultationType.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class CooldownPeriodExceededException extends BadRequestException {
    public CooldownPeriodExceededException(Integer consultationTypeId, Integer medicalCenterId) {
        super("Appointment does not respect the relationship cooldown period limit for consultation type: %d and medical center: %d".formatted(consultationTypeId, medicalCenterId));
    }
}