package com.turnera.turnera.consultationType.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class ConsultationTypeOnlyAcceptsHealthInsuranceException extends BadRequestException {
    public ConsultationTypeOnlyAcceptsHealthInsuranceException(Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        super("ConsultationType: " + consultationTypeId + " for professional: " + professionalId + " and medical center: " + medicalCenterId + " only accepts health insurance");
    }
}