package com.turnera.turnera.consultationType.application.find;

import com.turnera.turnera.consultationType.domain.ConsultationTypeService;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter {

  private final ConsultationTypeService consultationTypeService;

  public List<ConsultationTypeProfessionalMedicalCenterRelationship> find(
      Integer professionalId, Integer medicalCenterId) {
    return consultationTypeService
        .findConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter(
            professionalId, medicalCenterId);
  }
}
