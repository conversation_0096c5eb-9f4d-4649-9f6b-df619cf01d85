package com.turnera.turnera.consultationType.application.validations;

import static com.turnera.turnera.utils.LocalTimeUtils.isBetweenTimes;
import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndConsultationTypeForDate;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationInput;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.consultationType.domain.ConsultationTypeService;
import com.turnera.turnera.consultationType.domain.errors.CooldownPeriodExceededException;
import com.turnera.turnera.consultationType.domain.errors.PatientLimitExceededException;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeMedicalCenterRelationship;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ConsultationTypeValidations {

  private final ConsultationTypeService consultationTypeService;

  private final FindAppointmentsForMedicalCenterAndConsultationTypeForDate
      findAppointmentsForMedicalCenterAndConsultationTypeForDate;

  @Transactional
  public List<ConsultationTypeProfessionalMedicalCenterRelationship>
      verifyProfessionalMedicalCenterConsultationTypeRelationshipsExists(
          List<Integer> consultationTypeIds, Integer medicalCenterId, Integer professionalId) {
    log.info(
        APPLICATION,
        "Verifying if relationship exists for consultation types {}, medical center: {} and professional: {}",
        consultationTypeIds.size(),
        medicalCenterId,
        professionalId);
    List<ConsultationTypeProfessionalMedicalCenterRelationship> relationships =
        consultationTypeService
            .findProfessionalMedicalCenterRelationshipByConsultationIdsInMedicalCenterIdAndProfessionalId(
                consultationTypeIds, medicalCenterId, professionalId);
    log.info(
        APPLICATION,
        "Relationships exists for consultation types: {} medical center: {} and professional: {}",
        consultationTypeIds,
        medicalCenterId,
        professionalId);
    return relationships;
  }

  @Transactional
  public Optional<ConsultationTypeMedicalCenterRelationship>
      verifyAppointmentRespectsConsultationTypeMedicalCenterRelationship(
          AppointmentCreationInput input) {
    Integer consultationTypeId =
        input
            .getConsultationTypeProfessionalMedicalCenterRelationships()
            .getFirst()
            .getConsultationType()
            .getId();
    Integer medicalCenterId = input.getMedicalCenterId();
    log.info(
        APPLICATION,
        "Verifying if appointment respects the relationship limits for consultation type: {} and medical center: {}",
        consultationTypeId,
        medicalCenterId);
    Optional<ConsultationTypeMedicalCenterRelationship> maybeRelationship =
        consultationTypeService.findMaybeMedicalCenterRelationshipByIdAndMedicalCenterId(
            consultationTypeId, medicalCenterId);
    maybeRelationship.ifPresent(
        relationship -> {
          List<Appointment> appointments =
              findAppointmentsForMedicalCenterAndConsultationTypeForDate.find(
                  medicalCenterId, consultationTypeId, input.getDate());
          validateRespectsPatientLimit(relationship, appointments, input.getStartTime());
          validateRespectsCooldownPeriod(relationship, appointments, input.getStartTime());
        });
    log.info(
        APPLICATION,
        "Appointment respects the relationship limits for consultation type: {} and medical center: {}",
        consultationTypeId,
        medicalCenterId);
    return maybeRelationship;
  }

  private void validateRespectsPatientLimit(
      ConsultationTypeMedicalCenterRelationship relationship,
      List<Appointment> appointments,
      LocalTime newAppointmentStartTime) {
    List<Appointment> sameStartTimeAppointments =
        appointments.stream()
            .filter(appointment -> appointment.getStartTime().equals(newAppointmentStartTime))
            .toList();
    Integer consultationTypeId = relationship.getConsultationType().getId();
    Integer medicalCenterId = relationship.getMedicalCenter().getId();
    Optional.ofNullable(relationship.getPatientLimit())
        .ifPresent(
            patientLimit -> {
              if (sameStartTimeAppointments.size() >= patientLimit) {
                log.error(
                    APPLICATION,
                    "Appointment does not respect the relationship patient limit for consultation type: {} and medical center: {}",
                    consultationTypeId,
                    medicalCenterId);
                throw new PatientLimitExceededException(consultationTypeId, medicalCenterId);
              }
            });
  }

  private void validateRespectsCooldownPeriod(
      ConsultationTypeMedicalCenterRelationship relationship,
      List<Appointment> appointments,
      LocalTime newAppointmentStartTime) {
    Integer consultationTypeId = relationship.getConsultationType().getId();
    Integer medicalCenterId = relationship.getMedicalCenter().getId();
    Optional.ofNullable(relationship.getCooldownPeriod())
        .ifPresent(
            cooldownPeriod -> {
              appointments.forEach(
                  appointment -> {
                    LocalTime appointmentStartTime = appointment.getStartTime();
                    LocalTime appointmentCooldownPeriodEndTime =
                        appointmentStartTime.plusSeconds(cooldownPeriod.toSecondOfDay());
                    if (!newAppointmentStartTime.equals(appointmentCooldownPeriodEndTime)
                        && isBetweenTimes(
                            newAppointmentStartTime,
                            appointmentStartTime,
                            appointmentCooldownPeriodEndTime)) {
                      log.error(
                          APPLICATION,
                          "Appointment does not respect the relationship cooldown period limit for consultation type: {} and medical center: {}",
                          consultationTypeId,
                          medicalCenterId);
                      throw new CooldownPeriodExceededException(
                          consultationTypeId, medicalCenterId);
                    }
                  });
            });
  }
}
