package com.turnera.turnera.consultationType.infrastructure.entities;

import com.turnera.turnera.consultationType.presentation.entities.ConsultationTypeDTO;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "consultation_type")
public class ConsultationType {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "consultation_type_id_gen")
  @SequenceGenerator(
      name = "consultation_type_id_gen",
      sequenceName = "consultation_type_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @Size(max = 255)
  @NotNull
  @Column(name = "name", nullable = false)
  private String name;

  public ConsultationType() {}

  public ConsultationType(String name) {
    this.name = name;
  }

  public ConsultationTypeDTO toDTO() {
    return new ConsultationTypeDTO(this.id, this.name);
  }
}
