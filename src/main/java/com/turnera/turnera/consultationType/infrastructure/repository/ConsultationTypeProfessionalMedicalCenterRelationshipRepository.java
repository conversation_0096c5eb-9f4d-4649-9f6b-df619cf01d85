package com.turnera.turnera.consultationType.infrastructure.repository;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class ConsultationTypeProfessionalMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        ConsultationTypeProfessionalMedicalCenterRelationship,
        ConsultationTypeProfessionalMedicalCenterRelationshipId> {

  public List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findByConsultationTypeIdInAndMedicalCenterIdAndProfessionalId(
          List<Integer> consultationTypeIds, Integer medicalCenterId, Integer professionalId) {
    return find(
            "id.consultationTypeId IN ?1 AND id.medicalCenterId = ?2 AND id.professionalId = ?3",
            consultationTypeIds,
            medicalCenterId,
            professionalId)
        .list();
  }

  public List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findByIdMedicalCenterIdAndProfessionalId(Integer medicalCenterId, Integer professionalId) {
    return find(
            "id.medicalCenterId = ?1 AND id.professionalId = ?2", medicalCenterId, professionalId)
        .list();
  }
}
