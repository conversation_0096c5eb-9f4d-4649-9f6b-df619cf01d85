package com.turnera.turnera.consultationType.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.util.Objects;

@Setter
@Getter
@Embeddable
public class ConsultationTypeProfessionalMedicalCenterRelationshipId
    implements java.io.Serializable {
  private static final long serialVersionUID = -7567892566070392694L;

  @NotNull
  @Column(name = "consultation_type_id", nullable = false)
  private Integer consultationTypeId;

  @NotNull
  @Column(name = "professional_id", nullable = false)
  private Integer professionalId;

  @NotNull
  @Column(name = "medical_center_id", nullable = false)
  private Integer medicalCenterId;

  public ConsultationTypeProfessionalMedicalCenterRelationshipId() {}

  public ConsultationTypeProfessionalMedicalCenterRelationshipId(
      Integer id, Integer medicalCenterId, Integer professionalId) {
    this.consultationTypeId = id;
    this.medicalCenterId = medicalCenterId;
    this.professionalId = professionalId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    ConsultationTypeProfessionalMedicalCenterRelationshipId entity =
        (ConsultationTypeProfessionalMedicalCenterRelationshipId) o;
    return Objects.equals(this.medicalCenterId, entity.medicalCenterId)
        && Objects.equals(this.consultationTypeId, entity.consultationTypeId)
        && Objects.equals(this.professionalId, entity.professionalId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(medicalCenterId, consultationTypeId, professionalId);
  }
}
