package com.turnera.turnera.consultationType.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Embeddable
public class ConsultationTypeMedicalCenterRelationshipId implements java.io.Serializable {
    private static final long serialVersionUID = -7020692585962707661L;
    @NotNull
    @Column(name = "consultation_type_id", nullable = false)
    private Integer consultationTypeId;

    @NotNull
    @Column(name = "medical_center_id", nullable = false)
    private Integer medicalCenterId;


    public ConsultationTypeMedicalCenterRelationshipId() {
    }

    public ConsultationTypeMedicalCenterRelationshipId(Integer consultationTypeId, Integer medicalCenterId) {
        this.consultationTypeId = consultationTypeId;
        this.medicalCenterId = medicalCenterId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        ConsultationTypeMedicalCenterRelationshipId entity = (ConsultationTypeMedicalCenterRelationshipId) o;
        return Objects.equals(this.medicalCenterId, entity.medicalCenterId) &&
                Objects.equals(this.consultationTypeId, entity.consultationTypeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(medicalCenterId, consultationTypeId);
    }

}