package com.turnera.turnera.consultationType.infrastructure.entities;

import com.turnera.turnera.consultationType.domain.errors.ConsultationTypeOnlyAcceptsHealthInsuranceException;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ConsultationTypeDoctorRelationshipDTO;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "consultation_type_professional_medical_center_relationship")
public class ConsultationTypeProfessionalMedicalCenterRelationship {
  @EmbeddedId private ConsultationTypeProfessionalMedicalCenterRelationshipId id;

  @MapsId("consultationTypeId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "consultation_type_id", nullable = false)
  private ConsultationType consultationType;

  @MapsId("professionalId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @Column(name = "price")
  private BigDecimal price;

  @Column(name = "available_online", nullable = false)
  private Boolean availableOnline;

  @Column(name = "accepts_self_paid_patient", nullable = false)
  private Boolean acceptsSelfPaidPatient;

  @Column(name = "requires_medical_order", nullable = false)
  private Boolean requiresMedicalOrder;

  @Column(name = "appointment_interval_amount", nullable = false)
  private Integer appointmentIntervalAmount;

  @Column(name = "daily_limit")
  private Integer dailyLimit;

  @Column(name = "instructions")
  private String instructions;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "updated_by", nullable = false)
  private EmployeeUser updatedBy;

  @NotNull
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @OneToMany(
      mappedBy = "consultationTypeProfessionalMedicalCenterRelationship",
      fetch = FetchType.LAZY)
  private Set<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
      insuranceConsultationProfessionalMedicalCenterRelationships = new LinkedHashSet<>();

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "professional_id", insertable = false, updatable = false),
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false)
  })
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  public void validateAcceptsSelfPaidPatient() {
    if (!acceptsSelfPaidPatient || price == null) {
      throw new ConsultationTypeOnlyAcceptsHealthInsuranceException(
          consultationType.getId(), medicalCenter.getId(), professional.getId());
    }
  }

  public ConsultationTypeDoctorRelationshipDTO toDto() {
    return new ConsultationTypeDoctorRelationshipDTO(
        consultationType.getId(),
        consultationType.getName(),
        price,
        availableOnline,
        acceptsSelfPaidPatient,
        requiresMedicalOrder,
        appointmentIntervalAmount,
        instructions,
        dailyLimit,
        insuranceConsultationProfessionalMedicalCenterRelationships.stream()
            .map(HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship::toDto)
            .toList());
  }
}
