package com.turnera.turnera.consultationType.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@Entity
@Table(name = "consultation_type_medical_center_relationship")
public class ConsultationTypeMedicalCenterRelationship {
    @EmbeddedId
    private ConsultationTypeMedicalCenterRelationshipId id;

    @MapsId("consultationTypeId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "consultation_type_id", nullable = false)
    private ConsultationType consultationType;

    @MapsId("medicalCenterId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "medical_center_id", nullable = false)
    private MedicalCenter medicalCenter;

    @Column(name = "patient_limit")
    private Integer patientLimit;

    @Column(name = "cooldown_period")
    private LocalTime cooldownPeriod;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "created_by", nullable = false)
    private EmployeeUser createdBy;

    @NotNull
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "updated_by", nullable = false)
    private EmployeeUser updatedBy;

    @NotNull
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

}