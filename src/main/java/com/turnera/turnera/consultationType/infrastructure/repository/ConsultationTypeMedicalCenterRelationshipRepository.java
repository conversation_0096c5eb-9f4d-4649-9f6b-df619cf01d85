package com.turnera.turnera.consultationType.infrastructure.repository;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeMedicalCenterRelationship;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class ConsultationTypeMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        ConsultationTypeMedicalCenterRelationship, ConsultationTypeMedicalCenterRelationshipId> {}
