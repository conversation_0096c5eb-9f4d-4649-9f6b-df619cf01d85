package com.turnera.turnera.consultationType.infrastructure.adapter;

import com.turnera.turnera.consultationType.domain.ConsultationTypeService;
import com.turnera.turnera.consultationType.domain.errors.ConsultationTypeProfessionalMedicalCenterRelationshipNotFoundException;
import com.turnera.turnera.consultationType.infrastructure.entities.*;
import com.turnera.turnera.consultationType.infrastructure.repository.ConsultationTypeMedicalCenterRelationshipRepository;
import com.turnera.turnera.consultationType.infrastructure.repository.ConsultationTypeProfessionalMedicalCenterRelationshipRepository;
import com.turnera.turnera.consultationType.infrastructure.repository.ConsultationTypeRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class ConsultationTypeServiceImpl implements ConsultationTypeService {

  private final ConsultationTypeProfessionalMedicalCenterRelationshipRepository
      consultationTypeProfessionalMedicalCenterRelationshipRepository;

  private final ConsultationTypeRepository consultationTypeRepository;

  private final ConsultationTypeMedicalCenterRelationshipRepository
      consultationTypeMedicalCenterRelationshipRepository;

  @Override
  public List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findProfessionalMedicalCenterRelationshipByConsultationIdsInMedicalCenterIdAndProfessionalId(
          List<Integer> consultationTypeIds, Integer medicalCenterId, Integer professionalId) {
    List<ConsultationTypeProfessionalMedicalCenterRelationship> relationships =
        consultationTypeProfessionalMedicalCenterRelationshipRepository
            .findByConsultationTypeIdInAndMedicalCenterIdAndProfessionalId(
                consultationTypeIds, medicalCenterId, professionalId);
    if (relationships.size() != consultationTypeIds.size()) {
      Integer firstConsultationIdNotFound =
          consultationTypeIds.stream()
              .filter(
                  id ->
                      relationships.stream()
                          .noneMatch(
                              relationship ->
                                  relationship.getId().getConsultationTypeId().equals(id)))
              .findFirst()
              .orElse(null);
      throw new ConsultationTypeProfessionalMedicalCenterRelationshipNotFoundException(
          firstConsultationIdNotFound, medicalCenterId, professionalId);
    }
    return relationships;
  }

  @Override
  public Optional<ConsultationTypeMedicalCenterRelationship>
      findMaybeMedicalCenterRelationshipByIdAndMedicalCenterId(
          Integer consultationTypeId, Integer medicalCenterId) {
    return consultationTypeMedicalCenterRelationshipRepository.findByIdOptional(
        new ConsultationTypeMedicalCenterRelationshipId(consultationTypeId, medicalCenterId));
  }

  @Override
  public List<ConsultationTypeProfessionalMedicalCenterRelationship>
      findConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter(
          Integer professionalId, Integer medicalCenterId) {
    return consultationTypeProfessionalMedicalCenterRelationshipRepository
        .findByIdMedicalCenterIdAndProfessionalId(medicalCenterId, professionalId);
  }
}
