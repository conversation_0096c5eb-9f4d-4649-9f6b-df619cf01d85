package com.turnera.turnera.medicalCenter.presentation.entities.requests;

import com.turnera.turnera.medicalCenter.domain.entities.CreateMedicalCenterInput;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;

@Data
public class CreateMedicalCenterRequest {

  @NotBlank(message = "Email is required")
  private String mail;

  @NotBlank(message = "Phone is required")
  private String phone;

  private String imageUrl;

  @NotBlank(message = "Name is required")
  private String name;

  @NotBlank(message = "Address is required")
  private String address;

  @NotNull(message = "Postal code is required")
  private Integer postalCode;

  @NotBlank(message = "Province is required")
  private String province;

  @NotBlank(message = "City is required")
  private String city;

  @NotNull(message = "Accepts self paid patients flag is required")
  private Boolean acceptsSelfPaidPatients;

  private Integer floorNumber;

  private String departmentNumber;

  @NotNull(message = "Latitude is required")
  private BigDecimal latitude;

  @NotNull(message = "Longitude is required")
  private BigDecimal longitude;

  public CreateMedicalCenterInput toInput() {
    return new CreateMedicalCenterInput(
        getMail(),
        getPhone(),
        Optional.ofNullable(getImageUrl()),
        getName(),
        getAddress(),
        getPostalCode(),
        getProvince(),
        getCity(),
        getAcceptsSelfPaidPatients(),
        Optional.ofNullable(getFloorNumber()),
        Optional.ofNullable(getDepartmentNumber()),
        getLatitude(),
        getLongitude());
  }
}
