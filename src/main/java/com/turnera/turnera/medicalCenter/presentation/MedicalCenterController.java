package com.turnera.turnera.medicalCenter.presentation;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.application.create.CreateMedicalCenter;
import com.turnera.turnera.medicalCenter.application.find.FindGeneralInformation;
import com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterPatients;
import com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterProfessionalInformation;
import com.turnera.turnera.medicalCenter.application.find.FindProfessionalAgenda;
import com.turnera.turnera.medicalCenter.domain.entities.FindMedicalCenterInformationInput;
import com.turnera.turnera.medicalCenter.domain.entities.FindProfessionalScheduleInput;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.medicalCenter.presentation.entities.CreateMedicalCenterResponse;
import com.turnera.turnera.medicalCenter.presentation.entities.GeneralMedicalCenterInformationDTO;
import com.turnera.turnera.medicalCenter.presentation.entities.requests.CreateMedicalCenterRequest;
import com.turnera.turnera.patient.presentation.entities.PatientDTO;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import com.turnera.turnera.professional.presentation.entities.ProfessionalInformationDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.time.Month;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;

@Path("/medical-center")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class MedicalCenterController {

  private final CreateMedicalCenter createMedicalCenterService;
  private final FindMedicalCenterProfessionalInformation findMedicalCenterProfessionalInformation;
  private final EmployeeUserValidations employeeUserValidations;
  private final FindMedicalCenterPatients findMedicalCenterPatients;
  private final FindProfessionalAgenda findProfessionalAgenda;
  private final ProfessionalValidations professionalValidations;
  private final FindGeneralInformation findGeneralInformation;

  @POST
  public CreateMedicalCenterResponse createMedicalCenter(
      @Valid CreateMedicalCenterRequest request) {
    MedicalCenter medicalCenter = createMedicalCenterService.create(request.toInput());
    return new CreateMedicalCenterResponse(medicalCenter.getId());
  }

  @GET
  @Path("/{medicalCenterId}/initial-information")
  public List<ProfessionalInformationDTO> findInitialMedicalCenterInformation(
      @PathParam("medicalCenterId") Integer medicalCenterId,
      @QueryParam("employeeUserId") Integer employeeUserId) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            employeeUserId, medicalCenterId);
    return findMedicalCenterProfessionalInformation.find(
        employeeUser.getMedicalCenter(medicalCenterId),
        BuenosAiresTime.nowAsLocalDate().getMonth(),
        Year.of(BuenosAiresTime.nowAsLocalDate().getYear()));
  }

  @GET
  @Path("/{medicalCenterId}/patients")
  public List<PatientDTO> findMedicalCenterPatients(
      @PathParam("medicalCenterId") Integer medicalCenterId,
      @QueryParam("employeeUserId") Integer employeeUserId) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            employeeUserId, medicalCenterId);
    return findMedicalCenterPatients.findAll(employeeUser.getMedicalCenter(medicalCenterId));
  }

  @GET
  @Path("/{medicalCenterId}/general-information")
  @Transactional
  public GeneralMedicalCenterInformationDTO findGeneralInformation(
      @PathParam("medicalCenterId") Integer medicalCenterId,
      @QueryParam("employeeUserId") Integer employeeUserId) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            employeeUserId, medicalCenterId);
    return findGeneralInformation.find(
        new FindMedicalCenterInformationInput(
            employeeUser, employeeUser.getMedicalCenter(medicalCenterId)));
  }

  @GET
  @Path("/{medicalCenterId}/professional/{professionalId}/schedule")
  public ProfessionalAgendaDTO findProfessionalSchedule(
      @PathParam("medicalCenterId") Integer medicalCenterId,
      @QueryParam("employeeUserId") Integer employeeUserId,
      @PathParam("professionalId") Integer professionalId,
      @QueryParam("month") String month,
      @QueryParam("year") Integer year) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            employeeUserId, medicalCenterId);
    ProfessionalMedicalCenterRelationship professional =
        professionalValidations.verifyProfessionalBelongsToMedicalCenter(
            professionalId, medicalCenterId);
    FindProfessionalScheduleInput input =
        new FindProfessionalScheduleInput(
            employeeUser,
            professional,
            Month.valueOf(month.toUpperCase()),
            year != null ? Year.of(year) : Year.of(BuenosAiresTime.nowAsLocalDate().getYear()));
    return findProfessionalAgenda.find(input);
  }
}
