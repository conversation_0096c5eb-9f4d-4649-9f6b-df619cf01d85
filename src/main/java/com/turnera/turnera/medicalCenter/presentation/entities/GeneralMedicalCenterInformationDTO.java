package com.turnera.turnera.medicalCenter.presentation.entities;

import com.turnera.turnera.schedule.presentation.entities.ScheduleDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeneralMedicalCenterInformationDTO {
  private String establishmentName;

  private String logoUrl;

  private MedicalCenterAddressDTO address;
  private MedicalCenterContactDTO contact;
  private List<ScheduleDTO> schedule;
}
