package com.turnera.turnera.medicalCenter.domain.entities;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateMedicalCenterInput {
  private Integer parentMedicalCenterId;
  private String mail;
  private String phone;
  private Optional<String> imageUrl;
  private String name;
  private String address;
  private Integer postalCode;
  private String province;
  private String city;
  private Boolean acceptsSelfPaidPatients;
  private Optional<Integer> maybeFloorNumber;
  private Optional<String> maybeDepartmentNumber;
  private BigDecimal latitude;
  private BigDecimal longitude;

  public CreateMedicalCenterInput(
      String mail,
      String phone,
      Optional<String> imageUrl,
      String name,
      String address,
      Integer postalCode,
      String province,
      String city,
      Boolean acceptsSelfPaidPatients,
      Optional<Integer> maybeFloorNumber,
      Optional<String> maybeDepartmentNumber,
      BigDecimal latitude,
      BigDecimal longitude) {
    this.mail = mail;
    this.phone = phone;
    this.imageUrl = imageUrl;
    this.name = name;
    this.address = address;
    this.postalCode = postalCode;
    this.province = province;
    this.city = city;
    this.acceptsSelfPaidPatients = acceptsSelfPaidPatients;
    this.maybeFloorNumber = maybeFloorNumber;
    this.maybeDepartmentNumber = maybeDepartmentNumber;
    this.latitude = latitude;
    this.longitude = longitude;
  }

  public String toJson() {
    try {
      ObjectMapper mapper = new ObjectMapper();
      // Register the Jdk8Module to handle Optional properly
      mapper.registerModule(new Jdk8Module());
      return mapper.writeValueAsString(this);
    } catch (Exception e) {
      throw new RuntimeException("Error converting object to JSON", e);
    }
  }
}
