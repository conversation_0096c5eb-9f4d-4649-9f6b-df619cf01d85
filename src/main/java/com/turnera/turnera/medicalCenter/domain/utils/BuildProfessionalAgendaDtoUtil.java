package com.turnera.turnera.medicalCenter.domain.utils;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class BuildProfessionalAgendaDtoUtil {

  public ProfessionalAgendaDTO buildProfessionalScheduleDTO(
      Year year,
      Month month,
      ProfessionalMedicalCenterRelationship relationship,
      List<Appointment> appointments,
      List<BlockedSlot> blockedSlots) {
    LocalDate monthAndYearEndDate =
        LocalDate.of(year.getValue(), month, month.length(year.isLeap()));
    LocalDate monthAndYearStartDate = LocalDate.of(year.getValue(), month, 1);
    List<AppointmentSchedule> appointmentSchedules =
        relationship.getAppointmentSchedules().stream()
            .filter(
                schedule ->
                    !schedule.getStartingAt().isAfter(monthAndYearEndDate)
                        && schedule
                            .getEndingAt()
                            .map(endingAt -> !endingAt.isBefore(monthAndYearStartDate))
                            .orElse(true))
            .toList();
    List<SpecialSchedule> specialSchedules =
        relationship.getSpecialSchedules().stream()
            .filter(schedule -> schedule.isInMonthAndYear(month, year.getValue()))
            .toList();
    List<VacationSchedule> vacationSchedules =
        relationship.getVacationSchedules().stream()
            .filter(schedule -> schedule.isInMonthAndYear(month, year.getValue()))
            .toList();

    return new ProfessionalAgendaDTO(
        year.toString(),
        month.name(),
        appointmentSchedules.stream().map(AppointmentSchedule::toDTO).toList(),
        specialSchedules.stream().map(SpecialSchedule::toDTO).toList(),
        vacationSchedules.stream().map(VacationSchedule::toDTO).toList(),
        appointments.stream().map(Appointment::toDTO).toList(),
        blockedSlots.stream().map(BlockedSlot::toDTO).toList());
  }
}
