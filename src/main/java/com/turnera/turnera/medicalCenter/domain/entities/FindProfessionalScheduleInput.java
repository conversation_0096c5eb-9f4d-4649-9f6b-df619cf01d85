package com.turnera.turnera.medicalCenter.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import java.time.Month;
import java.time.Year;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class FindProfessionalScheduleInput {
  private final EmployeeUser employeeUser;
  private final ProfessionalMedicalCenterRelationship relationship;
  private final Month month;
  private final Year year;
}
