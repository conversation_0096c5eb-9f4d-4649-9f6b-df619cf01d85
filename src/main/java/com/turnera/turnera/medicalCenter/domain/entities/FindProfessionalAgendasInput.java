package com.turnera.turnera.medicalCenter.domain.entities;

import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import java.time.Month;
import java.time.Year;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class FindProfessionalAgendasInput {
  private final Integer medicalCenterId;
  private final Set<ProfessionalMedicalCenterRelationship> relationships;
  private final Month month;
  private final Year year;
}
