package com.turnera.turnera.medicalCenter.infrastructure.entities;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.medicalCenter.domain.entities.CreateMedicalCenterInput;
import com.turnera.turnera.medicalCenter.presentation.entities.MedicalCenterAddressDTO;
import com.turnera.turnera.medicalCenter.presentation.entities.MedicalCenterContactDTO;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.entities.ScheduleForDay;
import com.turnera.turnera.schedule.domain.entities.errors.AppointmentSchedulesCantBeSetDueToConflict;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.presentation.entities.ScheduleDTO;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Getter
@Setter
@Entity
@Table(name = "medical_center")
public class MedicalCenter {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "medical_center_id_gen")
  @SequenceGenerator(
      name = "medical_center_id_gen",
      sequenceName = "medical_center_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @ColumnDefault("gen_random_uuid()")
  @Column(name = "external_id")
  private UUID externalId;

  @Size(max = 255)
  @NotNull
  @Column(name = "mail", nullable = false)
  private String mail;

  @Size(max = 255)
  @NotNull
  @Column(name = "phone", nullable = false)
  private String phone;

  @Size(max = 255)
  @Column(name = "image_url")
  @Getter(AccessLevel.NONE)
  private String imageUrl;

  @Size(max = 255)
  @NotNull
  @Column(name = "name", nullable = false)
  private String name;

  @Size(max = 255)
  @NotNull
  @Column(name = "address", nullable = false)
  private String address;

  @NotNull
  @Column(name = "postal_code", nullable = false)
  private Integer postalCode;

  @Size(max = 255)
  @NotNull
  @Column(name = "province", nullable = false)
  private String province;

  @Size(max = 255)
  @NotNull
  @Column(name = "city", nullable = false)
  private String city;

  @ColumnDefault("true")
  @Column(name = "accepts_self_paid_patients")
  private Boolean acceptsSelfPaidPatients;

  @NotNull
  @Column(name = "floor_number")
  @Getter(AccessLevel.NONE)
  private Integer floorNumber;

  @NotNull
  @Column(name = "department_number")
  @Getter(AccessLevel.NONE)
  private String departmentNumber;

  @NotNull
  @Column(name = "latitude", nullable = false, precision = 9, scale = 6)
  private BigDecimal latitude;

  @NotNull
  @Column(name = "longitude", nullable = false, precision = 9, scale = 6)
  private BigDecimal longitude;

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<MedicalCenterSchedule> medicalCenterSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<AppointmentSchedule> appointmentSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<SpecialSchedule> specialSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<ProfessionalMedicalCenterRelationship> professionalMedicalCenterRelationships =
      new LinkedHashSet<>();

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<PatientMedicalCenterRelationship> patientMedicalCenterRelationships =
      new LinkedHashSet<>();

  @OneToMany(mappedBy = "medicalCenter", fetch = FetchType.LAZY)
  private Set<Appointment> appointments = new LinkedHashSet<>();

  public MedicalCenter() {}

  public MedicalCenter(CreateMedicalCenterInput input) {
    this.mail = input.getMail();
    this.phone = input.getPhone();
    this.imageUrl = input.getImageUrl().orElse(null);
    this.name = input.getName();
    this.address = input.getAddress();
    this.postalCode = input.getPostalCode();
    this.province = input.getProvince();
    this.city = input.getCity();
    this.acceptsSelfPaidPatients = input.getAcceptsSelfPaidPatients();
    this.floorNumber = input.getMaybeFloorNumber().orElse(null);
    this.departmentNumber = input.getMaybeDepartmentNumber().orElse(null);
    this.latitude = input.getLatitude();
    this.longitude = input.getLongitude();
  }

  public Optional<String> getMaybeImageUrl() {
    return Optional.ofNullable(imageUrl);
  }

  public Optional<Integer> getMaybeFloorNumber() {
    return Optional.ofNullable(floorNumber);
  }

  public Optional<String> getMaybeDepartmentNumber() {
    return Optional.ofNullable(departmentNumber);
  }

  public MedicalCenterAddressDTO toAddressDTO() {
    return new MedicalCenterAddressDTO(
        this.address,
        this.province,
        this.postalCode.toString(),
        "Argentina",
        getMaybeFloorNumber().map(Object::toString).orElse(null),
        getMaybeDepartmentNumber().orElse(null),
        this.latitude.toString(),
        this.longitude.toString());
  }

  public MedicalCenterContactDTO toContactDTO() {
    return new MedicalCenterContactDTO(this.phone, this.mail);
  }

  public List<ScheduleDTO> toListOfScheduleDTO() {
    return this.medicalCenterSchedules.stream()
        .map(MedicalCenterSchedule::toScheduleDto)
        .collect(Collectors.toList());
  }

  public void validateSchedulesFit(List<? extends ScheduleForDay> days, Integer professionalId) {
    for (ScheduleForDay day : days) {
      MedicalCenterSchedule medicalCenterScheduleForDay =
          medicalCenterSchedules.stream()
              .filter(schedule -> schedule.getDay().equals(day.getDayOfWeek()))
              .findFirst()
              .orElseThrow(
                  () ->
                      new AppointmentSchedulesCantBeSetDueToConflict(
                          getId(), professionalId, day.getDayOfWeek()));
      if (day.getStartTime().isBefore(medicalCenterScheduleForDay.getStartTime())
          || day.getEndTime().isAfter(medicalCenterScheduleForDay.getEndTime())) {
        throw new AppointmentSchedulesCantBeSetDueToConflict(
            getId(), professionalId, day.getDayOfWeek());
      }
    }
  }

  public Integer getDoctorsCount() {
    return professionalMedicalCenterRelationships.size();
  }
}
