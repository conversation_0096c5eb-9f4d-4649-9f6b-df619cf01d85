package com.turnera.turnera.medicalCenter.infrastructure.adapter;

import com.turnera.turnera.medicalCenter.domain.MedicalCenterService;
import com.turnera.turnera.medicalCenter.domain.entities.CreateMedicalCenterInput;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.medicalCenter.infrastructure.repository.MedicalCenterRepository;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class MedicalCenterServiceImpl implements MedicalCenterService {

  private final MedicalCenterRepository medicalCenterRepository;

  @Override
  public MedicalCenter findById(Integer medicalCenterId) {
    return medicalCenterRepository.findById(medicalCenterId);
  }

  @Override
  public MedicalCenter create(CreateMedicalCenterInput input) {
    MedicalCenter medicalCenter = new MedicalCenter(input);
    medicalCenterRepository.persist(medicalCenter);
    return medicalCenter;
  }
}
