package com.turnera.turnera.medicalCenter.application.sse.patients;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterPatients;
import com.turnera.turnera.medicalCenter.application.validations.MedicalCenterValidations;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.presentation.entities.PatientDTO;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class GetPatientsDtoForSse {

  private final FindMedicalCenterPatients findMedicalCenterPatients;
  private final MedicalCenterValidations medicalCenterValidations;
  private final ObjectMapper objectMapper;

  public SseEvent get(Integer medicalCenterId) {
    log.info("Fetching patients for medical center ID: {}", medicalCenterId);
    MedicalCenter medicalCenter =
        medicalCenterValidations.verifyMedicalCenterExists(medicalCenterId);
    List<PatientDTO> patients = findMedicalCenterPatients.findAll(medicalCenter);
    JsonNode data = objectMapper.valueToTree(patients);
    return new SseEvent("patients", data, System.currentTimeMillis());
  }
}
