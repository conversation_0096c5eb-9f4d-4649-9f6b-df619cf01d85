package com.turnera.turnera.medicalCenter.application.sse.patients;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterPatients;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.patient.presentation.entities.PatientDTO;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class GetPatientDtoForSse {

  private final FindMedicalCenterPatients findMedicalCenterPatients;
  private final ObjectMapper objectMapper;

  public SseEvent get(PatientMedicalCenterRelationship patientRelationship) {
    Integer patientId = patientRelationship.getId().getPatientId();
    Integer medicalCenterId = patientRelationship.getId().getMedicalCenterId();
    log.info("Fetching patient: {} for medical center: {}", patientId, medicalCenterId);
    PatientDTO patients = findMedicalCenterPatients.findByRelationship(patientRelationship);
    JsonNode data = objectMapper.valueToTree(patients);
    return new SseEvent("patient/" + patientId, data, System.currentTimeMillis());
  }
}
