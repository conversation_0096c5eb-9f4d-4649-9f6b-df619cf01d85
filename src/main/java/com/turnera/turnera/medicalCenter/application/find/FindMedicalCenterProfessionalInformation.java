package com.turnera.turnera.medicalCenter.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.application.find.FindFutureAppointmentsForMedicalCenterAndProfessional;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.domain.entities.FindProfessionalAgendasInput;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.domain.utils.ProfessionalDetailUtils;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import com.turnera.turnera.professional.presentation.entities.ProfessionalInformationDTO;
import com.turnera.turnera.specialty.application.find.FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindMedicalCenterProfessionalInformation {

  private final FindFutureAppointmentsForMedicalCenterAndProfessional
      findFutureAppointmentsForMedicalCenterAndProfessional;
  private final FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter
      findSpecialtiesRelationshipsByProfessionalAndMedicalCenter;
  private final FindMultipleProfessionalAgendas findMultipleProfessionalAgendas;
  private final ProfessionalDetailUtils professionalDetailUtils;

  public List<ProfessionalInformationDTO> find(
      MedicalCenter medicalCenter, Month month, Year year) {
    Integer medicalCenterId = medicalCenter.getId();
    log.info(APPLICATION, "Finding professionals for medical center: {}", medicalCenterId);
    Set<ProfessionalMedicalCenterRelationship> professionalMedicalCenterRelationships =
        medicalCenter.getProfessionalMedicalCenterRelationships();
    Map<Long, ProfessionalAgendaDTO> agendas =
        findMultipleProfessionalAgendas.find(
            new FindProfessionalAgendasInput(
                medicalCenterId, professionalMedicalCenterRelationships, month, year));

    return professionalMedicalCenterRelationships.stream()
        .map(
            relationship ->
                buildProfessionalInformationDTO(
                    relationship, agendas.get(relationship.getProfessionalId())))
        .collect(Collectors.toList());
  }

  private ProfessionalInformationDTO buildProfessionalInformationDTO(
      ProfessionalMedicalCenterRelationship relationship, ProfessionalAgendaDTO agendaDTO) {
    Professional professional = relationship.getProfessional();
    Integer medicalCenterId = relationship.getMedicalCenter().getId();
    List<String> specialties =
        findSpecialtiesRelationshipsByProfessionalAndMedicalCenter
            .find(professional.getId(), medicalCenterId)
            .stream()
            .map(SpecialtyProfessionalMedicalCenterRelationship::getSpecialtyName)
            .toList();
    List<Appointment> futureAppointments =
        findFutureAppointmentsForMedicalCenterAndProfessional.find(
            medicalCenterId, professional.getId());
    Long daysToNextAppointment = getDaysToNextAppointment(futureAppointments);
    Optional<Long> nextAppointmentQuantityForTomorrowOrToday =
        getNextAppointmentQuantityForTomorrow(futureAppointments);
    return new ProfessionalInformationDTO(
        professional.getId(),
        professional.getName(),
        professional.getSurname(),
        specialties,
        professional.getMedicalLicense(),
        daysToNextAppointment,
        nextAppointmentQuantityForTomorrowOrToday,
        agendaDTO,
        relationship.getAppointmentIntervalTime(),
        professionalDetailUtils.getHealthInsuranceInformation(
            professional.getId(), medicalCenterId),
        relationship.getConsultationTypeProfessionalMedicalCenterRelationships().stream()
            .map(ConsultationTypeProfessionalMedicalCenterRelationship::toDto)
            .toList(),
        relationship.toBookingPoliciesDTO());
  }

  private Long getDaysToNextAppointment(List<Appointment> appointments) {
    if (appointments == null || appointments.isEmpty()) {
      return null;
    }
    return appointments.stream()
        .map(Appointment::getDate)
        .min(Comparator.naturalOrder())
        .map(date -> ChronoUnit.DAYS.between(LocalDate.now(), date))
        .orElse(null);
  }

  private Optional<Long> getNextAppointmentQuantityForTomorrow(List<Appointment> appointments) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    LocalDate tomorrow = today.plusDays(1);
    List<Appointment> filteredAppointments =
        appointments.stream()
            .filter(
                appointment ->
                    appointment.getDate().equals(tomorrow) || appointment.getDate().equals(today))
            .toList();
    if (filteredAppointments.isEmpty()) return Optional.empty();
    return Optional.of((long) filteredAppointments.size());
  }
}
