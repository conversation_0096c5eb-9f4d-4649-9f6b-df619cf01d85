package com.turnera.turnera.medicalCenter.application.find;

import com.turnera.turnera.medicalCenter.domain.MedicalCenterService;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindMedicalCenterById {

  private final MedicalCenterService medicalCenterService;

  public MedicalCenter find(Integer medicalCenterId) {
    return medicalCenterService.findById(medicalCenterId);
  }
}
