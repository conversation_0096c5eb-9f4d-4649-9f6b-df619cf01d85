package com.turnera.turnera.medicalCenter.application.sse;

import com.turnera.turnera.medicalCenter.application.sse.doctor.GetProfessionalDtoForSse;
import com.turnera.turnera.medicalCenter.application.sse.patients.GetPatientsDtoForSse;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class GetAllMedicalCenterInformationForSse {

  private final GetPatientsDtoForSse getPatientsDtoForSse;
  private final GetProfessionalDtoForSse getProfessionalDtoForSse;

  @Transactional
  public List<SseEvent> get(Integer medicalCenterId) {
    List<SseEvent> sseEvents = new ArrayList<>();
    sseEvents.add(getPatientsDtoForSse.get(medicalCenterId));
    sseEvents.add(getProfessionalDtoForSse.get(medicalCenterId));
    return sseEvents;
  }
}
