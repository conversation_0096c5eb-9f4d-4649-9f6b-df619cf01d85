package com.turnera.turnera.medicalCenter.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.medicalCenter.domain.entities.FindMedicalCenterInformationInput;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.medicalCenter.presentation.entities.GeneralMedicalCenterInformationDTO;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindGeneralInformation {

  @Transactional
  public GeneralMedicalCenterInformationDTO find(FindMedicalCenterInformationInput input) {
    log.info(
        APPLICATION,
        "Finding general information for medical center with id: {}",
        input.getMedicalCenter().getId());
    MedicalCenter medicalCenter = input.getMedicalCenter();
    return new GeneralMedicalCenterInformationDTO(
        medicalCenter.getName(),
        medicalCenter.getMaybeImageUrl().orElse(null),
        medicalCenter.toAddressDTO(),
        medicalCenter.toContactDTO(),
        medicalCenter.toListOfScheduleDTO());
  }
}
