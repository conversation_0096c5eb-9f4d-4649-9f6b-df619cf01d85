package com.turnera.turnera.medicalCenter.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.appointment.presentation.entities.ShortenedAppointmentDTO;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.patient.presentation.entities.PatientDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindMedicalCenterPatients {

  public List<PatientDTO> findAll(MedicalCenter medicalCenter) {
    log.info(APPLICATION, "Finding patients for medical center with id: {}", medicalCenter.getId());
    Map<Integer, List<Appointment>> appointmentsByPatientId =
        medicalCenter.getAppointments().stream()
            .collect(Collectors.groupingBy(Appointment::getPatientId));
    List<PatientDTO> patients =
        medicalCenter.getPatientMedicalCenterRelationships().stream()
            .map(
                relationship ->
                    getPatientDtoFromRelationshipAndAppointments(
                        relationship,
                        appointmentsByPatientId.getOrDefault(
                            relationship.getPatient().getId(), List.of())))
            .collect(Collectors.toList());
    log.info(
        APPLICATION,
        "Found {} patients for medical center with id: {}",
        patients.size(),
        medicalCenter.getId());
    return patients;
  }

  public PatientDTO findByRelationship(PatientMedicalCenterRelationship relationship) {
    Integer medicalCenterId = relationship.getId().getMedicalCenterId();
    Integer patientId = relationship.getId().getPatientId();
    log.info(
        APPLICATION,
        "Finding patient with id: {} for medical center with id: {}",
        patientId,
        medicalCenterId);
    List<Appointment> patientAppointments = relationship.getAppointments().stream().toList();
    return getPatientDtoFromRelationshipAndAppointments(relationship, patientAppointments);
  }

  private PatientDTO getPatientDtoFromRelationshipAndAppointments(
      PatientMedicalCenterRelationship relationship, List<Appointment> appointments) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    List<ShortenedAppointmentDTO> patientAppointments =
        appointments.stream().map(Appointment::toShortenedDTO).toList();
    Patient patient = relationship.getPatient();
    List<ShortenedAppointmentDTO> futureAppointments =
        patientAppointments.stream()
            .filter(appointment -> !appointment.getDateTime().isBefore(now))
            .toList();
    List<ShortenedAppointmentDTO> pastAppointments =
        patientAppointments.stream()
            .filter(appointment -> appointment.getDateTime().isBefore(now))
            .toList();
    return new PatientDTO(
        patient.getId(),
        patient.getName() + " " + patient.getSurname(),
        patient.getMaybeUserId().orElse(null),
        patient.getIdentificationNumber(),
        patient.getPhone(),
        patient.getEmail(),
        patient.getHealthInsuranceName(),
        pastAppointments.size(),
        patient.getAttendancePercentage(),
        futureAppointments,
        pastAppointments);
  }
}
