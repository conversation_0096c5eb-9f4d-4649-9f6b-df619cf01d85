package com.turnera.turnera.medicalCenter.application.create;

import com.turnera.turnera.medicalCenter.domain.MedicalCenterService;
import com.turnera.turnera.medicalCenter.domain.entities.CreateMedicalCenterInput;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateMedicalCenter {

  private final MedicalCenterService medicalCenterService;

  public MedicalCenter create(CreateMedicalCenterInput input) {
    log.info("Creating medical center with input {}", input.toJson());
    MedicalCenter medicalCenter = medicalCenterService.create(input);

    log.info("Medical center created with id {}", medicalCenter.getId());
    return medicalCenter;
  }
}
