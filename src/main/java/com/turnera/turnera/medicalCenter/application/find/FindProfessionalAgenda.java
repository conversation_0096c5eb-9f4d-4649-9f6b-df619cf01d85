package com.turnera.turnera.medicalCenter.application.find;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForMonth;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsForMedicalCenterAndProfessionalForMonth;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.medicalCenter.domain.entities.FindProfessionalScheduleInput;
import com.turnera.turnera.medicalCenter.domain.utils.BuildProfessionalAgendaDtoUtil;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindProfessionalAgenda {

  private final FindAppointmentsForMedicalCenterAndProfessionalForMonth
      findAppointmentsForMedicalCenterAndProfessionalForMonth;
  private final FindBlockedSlotsForMedicalCenterAndProfessionalForMonth
      findBlockedSlotsForMedicalCenterAndProfessionalForMonth;
  private final BuildProfessionalAgendaDtoUtil buildProfessionalAgendaDtoUtil;

  public ProfessionalAgendaDTO find(FindProfessionalScheduleInput input) {
    ProfessionalMedicalCenterRelationship relationship = input.getRelationship();
    log.info(
        "Finding professional schedule for medical center: {}, professional: {}, month: {}",
        relationship.getMedicalCenter().getId(),
        relationship.getProfessional().getId(),
        input.getMonth());
    List<Appointment> appointments =
        findAppointmentsForMedicalCenterAndProfessionalForMonth.find(
            relationship.getMedicalCenter().getId(),
            relationship.getProfessional().getId(),
            input.getMonth());
    List<BlockedSlot> blockedSlots =
        findBlockedSlotsForMedicalCenterAndProfessionalForMonth.find(
            relationship.getMedicalCenter().getId(),
            relationship.getProfessional().getId(),
            input.getMonth());

    return buildProfessionalAgendaDtoUtil.buildProfessionalScheduleDTO(
        input.getYear(), input.getMonth(), relationship, appointments, blockedSlots);
  }
}
