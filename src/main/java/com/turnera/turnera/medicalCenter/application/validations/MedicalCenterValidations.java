package com.turnera.turnera.medicalCenter.application.validations;

import com.turnera.turnera.medicalCenter.application.find.FindMedicalCenterById;
import com.turnera.turnera.medicalCenter.domain.errors.MedicalCenterNotFoundException;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class MedicalCenterValidations {

  private final FindMedicalCenterById findMedicalCenterById;

  @Transactional
  public MedicalCenter verifyMedicalCenterExists(Integer medicalCenterId)
      throws MedicalCenterNotFoundException {
    MedicalCenter medicalCenter = findMedicalCenterById.find(medicalCenterId);
    if (medicalCenter == null) {
      throw new MedicalCenterNotFoundException(medicalCenterId);
    }
    return medicalCenter;
  }
}
