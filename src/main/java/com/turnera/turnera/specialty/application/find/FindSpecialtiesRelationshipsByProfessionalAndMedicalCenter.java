package com.turnera.turnera.specialty.application.find;

import com.turnera.turnera.specialty.domain.SpecialtyService;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter {

  private final SpecialtyService specialtyService;

  public List<SpecialtyProfessionalMedicalCenterRelationship> find(
      Integer professionalId, Integer medicalCenterId) {
    return specialtyService.findByProfessionalAndMedicalCenter(professionalId, medicalCenterId);
  }
}
