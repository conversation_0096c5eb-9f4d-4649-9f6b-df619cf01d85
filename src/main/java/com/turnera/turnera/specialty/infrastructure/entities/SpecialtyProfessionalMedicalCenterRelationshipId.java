package com.turnera.turnera.specialty.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

@Setter
@Getter
@Embeddable
public class SpecialtyProfessionalMedicalCenterRelationshipId implements java.io.Serializable {
  private static final long serialVersionUID = 3824985833519008105L;

  @NotNull
  @Column(name = "specialty_id", nullable = false)
  private Integer specialtyId;

  @NotNull
  @Column(name = "professional_id", nullable = false)
  private Integer professionalId;

  @NotNull
  @Column(name = "medical_center_id", nullable = false)
  private Integer medicalCenterId;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    SpecialtyProfessionalMedicalCenterRelationshipId entity =
        (SpecialtyProfessionalMedicalCenterRelationshipId) o;
    return Objects.equals(this.medicalCenterId, entity.medicalCenterId)
        && Objects.equals(this.specialtyId, entity.specialtyId)
        && Objects.equals(this.professionalId, entity.professionalId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(medicalCenterId, specialtyId, professionalId);
  }
}
