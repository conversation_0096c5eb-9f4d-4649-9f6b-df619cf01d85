package com.turnera.turnera.specialty.infrastructure.repository;

import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class SpecialtyProfessionalMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        SpecialtyProfessionalMedicalCenterRelationship,
        SpecialtyProfessionalMedicalCenterRelationshipId> {

  public List<SpecialtyProfessionalMedicalCenterRelationship>
      findByProfessionalIdAndMedicalCenterId(Integer professionalId, Integer medicalCenterId) {
    return find(
            "id.professionalId = ?1 AND id.medicalCenterId = ?2", professionalId, medicalCenterId)
        .list();
  }
}
