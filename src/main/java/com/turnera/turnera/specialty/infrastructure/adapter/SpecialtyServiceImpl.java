package com.turnera.turnera.specialty.infrastructure.adapter;

import com.turnera.turnera.specialty.domain.SpecialtyService;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import com.turnera.turnera.specialty.infrastructure.repository.SpecialtyProfessionalMedicalCenterRelationshipRepository;
import com.turnera.turnera.specialty.infrastructure.repository.SpecialtyRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class SpecialtyServiceImpl implements SpecialtyService {

  private final SpecialtyProfessionalMedicalCenterRelationshipRepository
      specialtyProfessionalMedicalCenterRelationshipRepository;

  private final SpecialtyRepository specialtyRepository;

  @Override
  public List<SpecialtyProfessionalMedicalCenterRelationship> findByProfessionalAndMedicalCenter(
      Integer professionalId, Integer medicalCenterId) {
    return specialtyProfessionalMedicalCenterRelationshipRepository
        .findByProfessionalIdAndMedicalCenterId(professionalId, medicalCenterId);
  }
}
