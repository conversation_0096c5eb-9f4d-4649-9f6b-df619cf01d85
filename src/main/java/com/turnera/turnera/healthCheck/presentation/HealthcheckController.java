package com.turnera.turnera.healthCheck.presentation;

import com.turnera.turnera.healthCheck.domain.HealthCheckService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.health.HealthCheckResponse;

@Path("/")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
public class HealthcheckController {

  private final HealthCheckService healthCheckService;

  @GET
  @Path("/healthcheck")
  public HealthCheckResponse health() {
    return healthCheckService.healthcheck();
  }
}
