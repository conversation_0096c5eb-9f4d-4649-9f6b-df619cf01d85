package com.turnera.turnera.healthCheck.infrastructure;

import com.turnera.turnera.healthCheck.domain.HealthCheckService;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.concurrent.ThreadLocalRandom;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;

@ApplicationScoped
public class HealthCheckServiceImpl implements HealthCheck, HealthCheckService {

  @Override
  public HealthCheckResponse call() {
    double chance = ThreadLocalRandom.current().nextDouble();
    if (chance > 0.9) {
      return HealthCheckResponse.down("turnera-service");
    }
    return HealthCheckResponse.up("turnera-service");
  }

  @Override
  public HealthCheckResponse healthcheck() {
    return call();
  }
}
