package com.turnera.turnera.utils.databaseUtils.slot;

import static com.turnera.turnera.utils.LocalTimeUtils.isBetweenTimes;
import static com.turnera.turnera.utils.LocalTimeUtils.multiplyLocalTime;
import static com.turnera.turnera.utils.StaticConstants.one;

import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.utils.BuenosAiresTime;
import com.turnera.turnera.utils.LocalTimeUtils;
import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
public abstract class Slot {

  @Column(nullable = false)
  private LocalDate date;

  @Column(name = "start_time", nullable = false)
  private LocalTime startTime;

  @Column(name = "appointment_interval_amount", nullable = false)
  private Integer appointmentIntervalAmount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "professional_id", nullable = false, insertable = false, updatable = false)
  private Professional professional;

  @Column(name = "professional_id", nullable = false)
  private Long professionalId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @Column(name = "created_at")
  private LocalDateTime createdAt;

  @Column(name = "updated_at")
  private LocalDateTime updatedAt;

  @Enumerated(EnumType.STRING)
  @Column(name = "creator_type", nullable = false)
  private UserType creatorType;

  @Column(name = "created_by")
  private Integer createdBy;

  @Enumerated(EnumType.STRING)
  @Column(name = "updater_type", nullable = false)
  private UserType updaterType;

  @Column(name = "updated_by")
  private Integer updatedBy;

  @PrePersist
  protected void onCreate() {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    createdAt = now;
    updatedAt = now;
  }

  @PreUpdate
  protected void onUpdate() {
    updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public LocalTime getEndTime(LocalTime appointmentIntervalTime) {
    return getStartTime()
        .plusSeconds(
            LocalTimeUtils.multiplyLocalTime(
                    appointmentIntervalTime, getAppointmentIntervalAmount() - one)
                .toSecondOfDay());
  }

  public boolean isBetweenIntervals(
      LocalTime intervalStartTime,
      LocalTime appointmentIntervalTime,
      Integer appointmentSlotDuration) {
    LocalTime intervalDuration =
        multiplyLocalTime(appointmentIntervalTime, appointmentSlotDuration);
    LocalTime intervalEndTime = intervalStartTime.plusSeconds(intervalDuration.toSecondOfDay());
    return isBetweenTimes(getStartTime(), intervalStartTime, intervalEndTime)
        || isBetweenTimes(intervalStartTime, getStartTime(), getEndTime(appointmentIntervalTime))
        || isBetweenTimes(intervalEndTime, getStartTime(), getEndTime(appointmentIntervalTime));
  }

  public boolean startOrEndEquals(LocalTime slotTime, LocalTime appointmentIntervalTime) {
    return getStartTime().equals(slotTime) || getEndTime(appointmentIntervalTime).equals(slotTime);
  }

  public boolean isMultiple() {
    return appointmentIntervalAmount > 1;
  }
}
