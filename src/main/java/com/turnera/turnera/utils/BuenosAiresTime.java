package com.turnera.turnera.utils;

import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@ApplicationScoped
public class BuenosAiresTime {

  public static LocalDateTime nowAsLocalDateTime() {
    ZoneId buenosAiresZone = ZoneId.of("America/Argentina/Buenos_Aires");
    ZonedDateTime buenosAiresTime = ZonedDateTime.now(buenosAiresZone);
    return buenosAiresTime.toLocalDateTime();
  }

  public static LocalDate nowAsLocalDate() {
    ZoneId buenosAiresZone = ZoneId.of("America/Argentina/Buenos_Aires");
    ZonedDateTime buenosAiresTime = ZonedDateTime.now(buenosAiresZone);
    return buenosAiresTime.toLocalDate();
  }
}
