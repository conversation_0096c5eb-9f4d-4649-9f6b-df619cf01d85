package com.turnera.turnera.utils;

import java.time.LocalDate;

public class LocalDateUtils {

    /**
     * Checks if the given time is between the start time (inclusive) and end time (exclusive).
     *
     * @param date  The time to check.
     * @param start The start time (inclusive).
     * @param end   The end time (exclusive).
     * @return True if the time is between start and end, false otherwise.
     */
    public static boolean isBetweenDates(LocalDate date, LocalDate start, LocalDate end) {
        // date is NOT before start AND time is NOT after end ( in between)
        return !date.isBefore(start) && !date.isAfter(end);

    }
}
