package com.turnera.turnera.utils;

import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

public class LogMarkers {
  public static final Marker APPLICATION = MarkerFactory.getMarker("APPLICATION");
  public static final Marker SSE = MarkerFactory.getMarker("SSE");
  public static final Marker SERVICE = MarkerFactory.getMarker("SERVICE");
  public static final Marker DATABASE = MarkerFactory.getMarker("DATABASE");
  public static final Marker PRESENTATION = MarkerFactory.getMarker("PRESENTATION");
}
