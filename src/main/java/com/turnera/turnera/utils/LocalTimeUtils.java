package com.turnera.turnera.utils;

import java.time.LocalTime;

public class LocalTimeUtils {

    /**
     * Checks if the given time is between the start time (inclusive) and end time (exclusive).
     *
     * @param time  The time to check.
     * @param start The start time (inclusive).
     * @param end   The end time (exclusive).
     * @return True if the time is between start and end, false otherwise.
     */
    public static boolean isBetweenTimes(LocalTime time, LocalTime start, LocalTime end) {
        // time is NOT before start AND time is NOT after end ( in between)
        return !time.isBefore(start) && !time.isAfter(end);

    }

    /**
     * Checks if the given time is strictly between the start time (exclusive) and end time (exclusive).
     *
     * @param time  The time to check.
     * @param start The start time (exclusive).
     * @param end   The end time (exclusive).
     * @return True if the time is strictly between start and end, false otherwise.
     */
    public static boolean isStrictlyBetweenTimes(LocalTime time, LocalTime start, LocalTime end) {
        // time is after start AND time is before end (strictly between)
        return time.isAfter(start) && time.isBefore(end);
    }


    public static LocalTime multiplyLocalTime(LocalTime time, int multiplier) throws IllegalArgumentException {
        // Convert LocalTime to total seconds since midnight
        long totalSeconds = time.toSecondOfDay();

        // Multiply by the multiplier
        long multipliedSeconds = totalSeconds * multiplier;

        // Check if the result exceeds 24 hours (86400 seconds)
        if (multipliedSeconds >= 86400) {
            throw new IllegalArgumentException("The resulting time exceeds 24 hours.");
        }

        // Convert back to LocalTime
        return LocalTime.ofSecondOfDay(multipliedSeconds);
    }


}
