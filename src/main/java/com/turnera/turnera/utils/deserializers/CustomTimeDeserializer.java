package com.turnera.turnera.utils.deserializers;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@ApplicationScoped
public class CustomTimeDeserializer extends JsonDeserializer<LocalTime> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public LocalTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String timeString = p.getText().trim();
        try {
            return LocalTime.parse(timeString, FORMATTER);
        } catch (DateTimeParseException e) {
            throw new InvalidFormatException(
                    p,
                    "Time must be in format HH:mm:ss (e.g., 08:30:00)",
                    timeString,
                    LocalTime.class
            );
        }
    }
}