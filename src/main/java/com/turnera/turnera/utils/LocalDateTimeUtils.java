package com.turnera.turnera.utils;

import java.time.LocalDateTime;

public class LocalDateTimeUtils {

    /**
     * Checks if the given date-time is between the start date-time (inclusive) and end date-time (exclusive).
     *
     * @param dateTime The date-time to check.
     * @param start    The start date-time (inclusive).
     * @param end      The end date-time (exclusive).
     * @return True if the date-time is between start and end, false otherwise.
     */
    public static boolean isBetween(LocalDateTime dateTime, LocalDateTime start, LocalDateTime end) {
        // dateTime is NOT before start AND dateTime is NOT after end (in between)
        return !dateTime.isBefore(start) && !dateTime.isAfter(end);
    }
}