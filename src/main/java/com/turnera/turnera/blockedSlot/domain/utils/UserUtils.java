package com.turnera.turnera.blockedSlot.domain.utils;

import static com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType.DOCTOR;
import static com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType.PATIENT;
import static com.turnera.turnera.utils.UserType.EMPLOYEE_USER;

import com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType;
import com.turnera.turnera.user.infrastructure.entities.User;

public class UserUtils {
  public static BlockedSlotType getBlockedSlotType(User user) {
    if (user.getUserType().equals(EMPLOYEE_USER)) {
      return DOCTOR;
    }
    return PATIENT;
  }
}
