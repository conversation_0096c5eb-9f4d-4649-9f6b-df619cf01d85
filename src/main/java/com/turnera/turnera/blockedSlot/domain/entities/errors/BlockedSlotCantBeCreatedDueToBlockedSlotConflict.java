package com.turnera.turnera.blockedSlot.domain.entities.errors;

import com.turnera.turnera.blockedSlot.domain.entities.BlockedSlotCreationInput;
import com.turnera.turnera.configuration.exceptions.ConflictException;
import java.time.format.DateTimeFormatter;

public class BlockedSlotCantBeCreatedDueToBlockedSlotConflict extends ConflictException {

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy");

  public BlockedSlotCantBeCreatedDueToBlockedSlotConflict(
      Integer medicalCenterId,
      Integer professionalId,
      BlockedSlotCreationInput input,
      Integer conflictingBlockedSlotId) {
    super(
        String.format(
            "Cannot create blocked slot for medical center with ID %d and professional with ID %d on date %s "
                + "because of a conflict with existing blocked slot %s",
            medicalCenterId,
            professionalId,
            input.getDate().format(DATE_FORMATTER),
            conflictingBlockedSlotId));
  }
}
