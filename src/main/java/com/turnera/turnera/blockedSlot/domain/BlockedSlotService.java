package com.turnera.turnera.blockedSlot.domain;

import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import java.time.LocalDate;
import java.time.Month;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface BlockedSlotService {
  BlockedSlot save(BlockedSlot blockedSlot);

  Optional<BlockedSlot> findById(Integer id);

  List<BlockedSlot> findByProfessionalIdAndMedicalCenterIdAndDate(
      Integer professionalId, Integer medicalCenterId, LocalDate date);

  List<BlockedSlot> findByMedicalCenterIdAndProfessionalIdAndMonth(
      Integer medicalCenterId, Integer professionalId, Month month);

  void deleteById(Integer id);

  boolean existsById(Integer id);

  Set<BlockedSlot> findByMedicalCenterIdAndMonth(Integer medicalCenterId, Month month);
}
