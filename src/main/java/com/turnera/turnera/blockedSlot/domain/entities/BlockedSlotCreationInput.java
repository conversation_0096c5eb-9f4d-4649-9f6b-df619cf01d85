package com.turnera.turnera.blockedSlot.domain.entities;

import static com.turnera.turnera.blockedSlot.domain.utils.UserUtils.getBlockedSlotType;
import static com.turnera.turnera.utils.UserType.EMPLOYEE_USER;

import com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.entities.AppointmentCreationValidationInput;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.utils.LocalTimeUtils;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class BlockedSlotCreationInput {

  private LocalDate date;
  private LocalTime startTime;
  private Integer appointmentSlotDuration;
  private String reason;
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;
  private Optional<Patient> patient;
  private User creator;

  public BlockedSlotCreationInput(
      LocalDate date,
      LocalTime startTime,
      Integer appointmentSlotDuration,
      String reason,
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship,
      Patient patient,
      User creator) {
    this.date = date;
    this.startTime = startTime;
    this.appointmentSlotDuration = appointmentSlotDuration;
    this.reason = reason;
    this.professionalMedicalCenterRelationship = professionalMedicalCenterRelationship;
    this.patient = Optional.ofNullable(patient);
    this.creator = creator;
  }

  public BlockedSlot toRow() {
    BlockedSlot blockedSlot = new BlockedSlot();
    blockedSlot.setDate(date);
    blockedSlot.setStartTime(startTime);
    blockedSlot.setAppointmentIntervalAmount(appointmentSlotDuration);
    blockedSlot.setSlotType(getType());
    blockedSlot.setProfessional(professionalMedicalCenterRelationship.getProfessional());
    blockedSlot.setPatient(patient.orElse(null));
    blockedSlot.setMedicalCenter(professionalMedicalCenterRelationship.getMedicalCenter());
    blockedSlot.setCreatorType(creator.getUserType());
    blockedSlot.setCreatedBy(creator.getId());
    blockedSlot.setUpdaterType(creator.getUserType());
    blockedSlot.setUpdatedBy(creator.getId());

    return blockedSlot;
  }

  public Boolean isMedicalCenterSideAgenda() {
    return creator.getUserType().equals(EMPLOYEE_USER);
  }

  public BlockedSlotType getType() {
    return getBlockedSlotType(creator);
  }

  public LocalTime getEndTime(LocalTime appointmentIntervalTime) {
    return getStartTime()
        .plusSeconds(
            LocalTimeUtils.multiplyLocalTime(appointmentIntervalTime, getAppointmentSlotDuration())
                .toSecondOfDay());
  }

  public AppointmentCreationValidationInput toAppointmentCreationValidationInput() {
    return new AppointmentCreationValidationInput(
        professionalMedicalCenterRelationship,
        date,
        startTime,
        appointmentSlotDuration,
        isMedicalCenterSideAgenda());
  }
}
