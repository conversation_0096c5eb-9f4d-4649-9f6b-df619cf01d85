package com.turnera.turnera.blockedSlot.application.find;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Month;
import java.time.Year;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindBlockedSlotsForMedicalCenterInMonthAndYear {

  private final BlockedSlotService blockedSlotService;

  public Set<BlockedSlot> find(Integer medicalCenterId, Month month, Year year) {
    log.info("Finding blocked slots for medical center: {} in month: {} and year: {}", medicalCenterId, month, year);
    return blockedSlotService.findByMedicalCenterIdAndMonthAndYear(medicalCenterId, month, year);
  }
}
