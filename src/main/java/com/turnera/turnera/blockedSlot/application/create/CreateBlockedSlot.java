package com.turnera.turnera.blockedSlot.application.create;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForDate;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.domain.entities.BlockedSlotCreationInput;
import com.turnera.turnera.blockedSlot.domain.entities.errors.BlockedSlotCantBeCreatedDueToConflict;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.application.validations.ScheduleValidations;
import com.turnera.turnera.schedule.application.validations.ValidateAgendaForSlotCreation;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateBlockedSlot {

  private final BlockedSlotService blockedSlotService;

  private final FindAppointmentsForMedicalCenterAndProfessionalForDate
      findAppointmentsForMedicalCenterAndProfessionalForDate;

  private final ScheduleValidations scheduleValidations;

  private final ValidateAgendaForSlotCreation validateAgendaForSlotCreation;

  @Transactional
  public BlockedSlot execute(BlockedSlotCreationInput input) {
    Professional professional = input.getProfessionalMedicalCenterRelationship().getProfessional();
    MedicalCenter medicalCenter =
        input.getProfessionalMedicalCenterRelationship().getMedicalCenter();
    log.info(
        "Creating blocked slot for professional with ID: {} and medical center with ID: {}",
        professional.getId(),
        medicalCenter.getId());
    AppointmentSchedule appointmentSchedule =
        scheduleValidations.validateScheduleExistsForDateAndProfessionalMedicalCenterRelationship(
            input.getDate(), input.getProfessionalMedicalCenterRelationship());
    validateNoConflicts(input, appointmentSchedule);
    validateAgendaForSlotCreation.validate(input.toAppointmentCreationValidationInput());
    BlockedSlot savedBlockedSlot = blockedSlotService.save(input.toRow());
    log.info(
        "Blocked slot created with ID: {} for professional with ID: {} and medical center with ID: {}",
        savedBlockedSlot.getId(),
        professional.getId(),
        medicalCenter.getId());
    return savedBlockedSlot;
  }

  private void validateNoConflicts(
      BlockedSlotCreationInput input, AppointmentSchedule appointmentSchedule) {
    if (input.isMedicalCenterSideAgenda()) {
      validateNoConflictWithExistingAppointments(input, appointmentSchedule);
    }
  }

  private void validateNoConflictWithExistingAppointments(
      BlockedSlotCreationInput input, AppointmentSchedule appointmentSchedule) {
    log.info("Validating no conflicts with existing appointments");
    ProfessionalMedicalCenterRelationship relationship =
        input.getProfessionalMedicalCenterRelationship();
    Professional professional = relationship.getProfessional();
    MedicalCenter medicalCenter = relationship.getMedicalCenter();

    List<Appointment> futureAppointments =
        findAppointmentsForMedicalCenterAndProfessionalForDate.find(
            medicalCenter.getId(), professional.getId(), input.getDate());

    futureAppointments.forEach(
        appointment -> {
          if (appointment.isBetweenIntervals(
              input.getStartTime(),
              relationship.getAppointmentIntervalTime(),
              input.getAppointmentSlotDuration())) {
            log.error(
                "Conflict detected with existing appointment ID: {} for professional ID: {} in medical center ID: {}",
                appointment.getId(),
                professional.getId(),
                medicalCenter.getId());
            throw new BlockedSlotCantBeCreatedDueToConflict(
                medicalCenter.getId(), professional.getId(), input, appointment.getId());
          }
        });
    log.info("No conflicts with existing appointments found, validation complete");
  }
}
