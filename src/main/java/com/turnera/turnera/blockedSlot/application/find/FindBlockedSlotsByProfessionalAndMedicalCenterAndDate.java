package com.turnera.turnera.blockedSlot.application.find;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindBlockedSlotsByProfessionalAndMedicalCenterAndDate {

  private final BlockedSlotService blockedSlotService;

  public List<BlockedSlot> find(Integer professionalId, Integer medicalCenterId, LocalDate date) {
    log.info(
        "Finding blocked slots for professional ID: {} in medical center ID: {} on date: {}",
        professionalId,
        medicalCenterId,
        date);
    List<BlockedSlot> slots =
        blockedSlotService.findByProfessionalIdAndMedicalCenterIdAndDate(
            professionalId, medicalCenterId, date);
    log.info(
        "Found {} blocked slots for professional ID: {} in medical center ID: {} on date: {}",
        slots.size(),
        professionalId,
        medicalCenterId,
        date);
    return slots;
  }
}
