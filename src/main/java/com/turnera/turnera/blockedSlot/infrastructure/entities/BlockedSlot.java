package com.turnera.turnera.blockedSlot.infrastructure.entities;

import static com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType.DOCTOR;
import static com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType.PATIENT;

import com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType;
import com.turnera.turnera.blockedSlot.presentation.entities.BlockedSlotDTO;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.utils.databaseUtils.slot.Slot;
import jakarta.persistence.*;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "blocked_slot")
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class BlockedSlot extends Slot {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Enumerated(EnumType.STRING)
  @Column(name = "slot_type", nullable = false)
  private BlockedSlotType slotType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "patient_id")
  private Patient patient;

  public BlockedSlot() {}

  public boolean isDoctorSide() {
    return this.slotType == DOCTOR;
  }

  public boolean isPatientSide() {
    return this.slotType == PATIENT;
  }

  public BlockedSlotDTO toDTO() {
    return new BlockedSlotDTO(
        this.getId().longValue(),
        this.getDate().toString(),
        this.slotType,
        Optional.ofNullable(this.patient).map(Patient::getId).orElse(null),
        this.getStartTime().toString(),
        this.getAppointmentIntervalAmount());
  }
}
