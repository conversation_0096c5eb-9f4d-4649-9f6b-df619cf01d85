package com.turnera.turnera.blockedSlot.infrastructure.repository;

import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApplicationScoped
public class BlockedSlotRepository implements PanacheRepositoryBase<BlockedSlot, Integer> {

  public List<BlockedSlot> findByProfessionalIdAndMedicalCenterIdAndDate(
      Integer professionalId, Integer medicalCenterId, LocalDate date) {
    return find(
            "professional.id = ?1 AND medicalCenter.id = ?2 AND date = ?3",
            professionalId,
            medicalCenterId,
            date)
        .list();
  }

  public List<BlockedSlot> findByMedicalCenterIdAndProfessionalIdAndMonth(
      Integer medicalCenterId, Integer professionalId, Integer month) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND EXTRACT(MONTH FROM date) = ?3",
            medicalCenterId,
            professionalId,
            month)
        .list();
  }

  public Set<BlockedSlot> findDistinctByMedicalCenterIdAndMonth(
      Integer medicalCenterId, Integer month) {
    return find(
            "SELECT DISTINCT bs FROM BlockedSlot bs WHERE bs.medicalCenter.id = ?1 AND EXTRACT(MONTH FROM bs.date) = ?2",
            medicalCenterId,
            month)
        .stream()
        .collect(Collectors.toSet());
  }

  public Set<BlockedSlot> findDistinctByMedicalCenterIdAndMonthAndYear(
      Integer medicalCenterId, Integer month, Integer year) {
    return find(
            "SELECT DISTINCT bs FROM BlockedSlot bs WHERE bs.medicalCenter.id = ?1 AND EXTRACT(MONTH FROM bs.date) = ?2 AND EXTRACT(YEAR FROM bs.date) = ?3",
            medicalCenterId,
            month,
            year)
        .stream()
        .collect(Collectors.toSet());
  }
}
