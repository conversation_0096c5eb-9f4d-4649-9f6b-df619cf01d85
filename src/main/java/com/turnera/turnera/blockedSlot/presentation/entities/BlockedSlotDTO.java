package com.turnera.turnera.blockedSlot.presentation.entities;

import com.turnera.turnera.blockedSlot.domain.entities.enums.BlockedSlotType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlockedSlotDTO {
  private Long id;
  private String date;

  private BlockedSlotType slotType;

  private Integer patientId;

  private String startTime;

  private int appointmentIntervalAmount;
}
