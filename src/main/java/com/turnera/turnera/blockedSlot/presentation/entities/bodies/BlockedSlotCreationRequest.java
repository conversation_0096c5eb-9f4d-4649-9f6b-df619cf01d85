package com.turnera.turnera.blockedSlot.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.turnera.turnera.blockedSlot.domain.entities.BlockedSlotCreationInput;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.utils.deserializers.CustomDateDeserializer;
import com.turnera.turnera.utils.deserializers.CustomTimeDeserializer;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
public class BlockedSlotCreationRequest {

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  @JsonDeserialize(using = CustomDateDeserializer.class)
  private LocalDate date;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  @JsonDeserialize(using = CustomTimeDeserializer.class)
  private LocalTime startTime;

  @NotNull private Integer appointmentSlotDuration;

  private String reason;

  @NotNull private Integer professionalId;

  @NotNull private Integer medicalCenterId;

  public BlockedSlotCreationInput toDomain(
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship,
      Patient patient,
      User creator) {
    return new BlockedSlotCreationInput(
        date,
        startTime,
        appointmentSlotDuration,
        reason,
        professionalMedicalCenterRelationship,
        patient,
        creator);
  }
}
