package com.turnera.turnera.blockedSlot.presentation;

import com.turnera.turnera.blockedSlot.application.create.CreateBlockedSlot;
import com.turnera.turnera.blockedSlot.domain.entities.BlockedSlotCreationInput;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.blockedSlot.presentation.entities.bodies.BlockedSlotCreationRequest;
import com.turnera.turnera.blockedSlot.presentation.entities.bodies.BlockedSlotCreationRequestFromMedicalCenter;
import com.turnera.turnera.blockedSlot.presentation.entities.bodies.BlockedSlotCreationRequestFromTurneraUser;
import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.patient.application.validations.PatientValidations;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.turneraUser.application.validations.UserValidations;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import com.turnera.turnera.user.infrastructure.entities.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;

@Path("/api/blocked-slots")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class BlockedSlotController {

  private final CreateBlockedSlot createBlockedSlot;
  private final ProfessionalValidations professionalValidations;
  private final PatientValidations patientValidations;
  private final EmployeeUserValidations employeeUserValidations;
  private final UserValidations userValidations;

  private Response createBlockedSlot(
      BlockedSlotCreationRequest request, User creator, Patient patient) {
    ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship =
        professionalValidations.verifyProfessionalBelongsToMedicalCenter(
            request.getProfessionalId(), request.getMedicalCenterId());

    BlockedSlotCreationInput input =
        request.toDomain(professionalMedicalCenterRelationship, patient, creator);

    BlockedSlot blockedSlot = createBlockedSlot.execute(input);
    return Response.status(Response.Status.CREATED).entity(blockedSlot).build();
  }

  @POST
  @Path("/by-medical-center")
  public Response createBlockedSlotByMedicalCenter(
      @Valid BlockedSlotCreationRequestFromMedicalCenter request) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    return createBlockedSlot(request, employeeUser, null);
  }

  @POST
  @Path("/by-turnera-user")
  public Response createBlockedSlotByTurneraUser(
      @Valid BlockedSlotCreationRequestFromTurneraUser request) {
    TurneraUser turneraUser =
        userValidations.verifyUserExistsAndIsActive(request.getTurneraUserId());
    Patient patient = patientValidations.verifyPatientExists(request.getPatientId());
    return createBlockedSlot(request, turneraUser, patient);
  }
}
