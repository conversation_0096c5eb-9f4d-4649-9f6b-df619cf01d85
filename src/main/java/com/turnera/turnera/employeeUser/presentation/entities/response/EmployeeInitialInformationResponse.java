package com.turnera.turnera.employeeUser.presentation.entities.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeInitialInformationResponse {
  private Integer id;
  private String name;
  private String surname;
  private List<MedicalCenterInformationResponse> medicalCenters;
}
