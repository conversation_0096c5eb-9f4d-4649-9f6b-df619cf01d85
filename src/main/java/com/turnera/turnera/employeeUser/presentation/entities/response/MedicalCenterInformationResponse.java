package com.turnera.turnera.employeeUser.presentation.entities.response;

import com.turnera.turnera.employeeUser.domain.entities.EmployeeUserType;
import com.turnera.turnera.schedule.presentation.entities.ScheduleDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MedicalCenterInformationResponse {
  private Integer id;
  private String address;
  private String name;
  private String phoneNumber;
  private Integer doctorsCount;
  private EmployeeUserType role;
  private List<ScheduleDTO> schedules;
}
