package com.turnera.turnera.employeeUser.presentation;

import com.turnera.turnera.employeeUser.application.find.FindEmployeeUserByAuth0Id;
import com.turnera.turnera.employeeUser.application.find.GetEmployeeUserMedicalCenters;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.employeeUser.presentation.entities.response.EmployeeInitialInformationResponse;
import com.turnera.turnera.employeeUser.presentation.entities.response.MedicalCenterInformationResponse;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;
import lombok.RequiredArgsConstructor;

@Path("/employee-user")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
public class EmployeeUserController {

  private final GetEmployeeUserMedicalCenters getEmployeeUserMedicalCenters;
  private final FindEmployeeUserByAuth0Id findEmployeeUserByAuth0Id;

  @GET
  @Path("/{employeeUserId}/medical-centers")
  public List<MedicalCenterInformationResponse> getMedicalCenters(
      @PathParam("employeeUserId") Integer employeeUserId) {
    return getEmployeeUserMedicalCenters.execute(employeeUserId);
  }

  @GET
  @Path("/{auth0Id}")
  public EmployeeInitialInformationResponse getEmployeeUserByAuth0Id(
      @PathParam("auth0Id") String auth0Id) {
    EmployeeUser employeeUser = findEmployeeUserByAuth0Id.find(auth0Id);
    return employeeUser.toEmployeeInitialInformationResponse();
  }
}
