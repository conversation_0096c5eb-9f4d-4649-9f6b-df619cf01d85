package com.turnera.turnera.employeeUser.application.validations;

import com.turnera.turnera.employeeUser.application.find.FindEmployeeUserById;
import com.turnera.turnera.employeeUser.domain.errors.EmployeeUserNotFoundException;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class EmployeeUserValidations {

  private final FindEmployeeUserById findEmployeeUserById;

  @Transactional
  public EmployeeUser verifyEmployeeUserExists(Integer userId)
      throws EmployeeUserNotFoundException {
    EmployeeUser user = findEmployeeUserById.find(userId);
    if (user == null) {
      throw new EmployeeUserNotFoundException(userId);
    }
    return user;
  }

  @Transactional
  public EmployeeUser verifyEmployeeUserExistsAndHasPermissions(
      Integer userId, Integer medicalCenterId) throws EmployeeUserNotFoundException {
    EmployeeUser user = verifyEmployeeUserExists(userId);
    verifyEmployeeUserPermissions(user, medicalCenterId);
    return user;
  }

  private void verifyEmployeeUserPermissions(EmployeeUser user, Integer medicalCenterId) {
    if (user.getEmployeeUserMedicalCenterRelationships().stream()
        .noneMatch(
            relationship -> relationship.getId().getMedicalCenterId().equals(medicalCenterId))) {
      throw new SecurityException("Employee user does not have permissions to perform this action");
    }
  }
}
