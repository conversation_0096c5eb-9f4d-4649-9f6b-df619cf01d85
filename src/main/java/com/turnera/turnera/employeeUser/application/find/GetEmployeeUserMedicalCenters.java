package com.turnera.turnera.employeeUser.application.find;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUserMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.presentation.entities.response.MedicalCenterInformationResponse;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class GetEmployeeUserMedicalCenters {

  private final EmployeeUserValidations employeeUserValidations;

  public List<MedicalCenterInformationResponse> execute(Integer employeeUserId) {
    log.info("Finding medical centers for employee user with ID: {}", employeeUserId);
    EmployeeUser employeeUser = employeeUserValidations.verifyEmployeeUserExists(employeeUserId);
    List<MedicalCenterInformationResponse> medicalCenters =
        employeeUser.getEmployeeUserMedicalCenterRelationships().stream()
            .map(EmployeeUserMedicalCenterRelationship::mapToResponse)
            .collect(Collectors.toList());
    log.info(
        "Found {} medical centers for employee user with ID: {}",
        medicalCenters.size(),
        employeeUserId);
    return medicalCenters;
  }
}
