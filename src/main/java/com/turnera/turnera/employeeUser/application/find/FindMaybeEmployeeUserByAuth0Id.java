package com.turnera.turnera.employeeUser.application.find;

import com.turnera.turnera.employeeUser.domain.EmployeeUserService;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindMaybeEmployeeUserByAuth0Id {

  private final EmployeeUserService employeeUserService;

  public Optional<EmployeeUser> find(String auth0Id) {
    log.info("Finding employee user with auth0 ID: {}", auth0Id);
    Optional<EmployeeUser> maybeEmployeeUser = employeeUserService.findMaybeByAuth0Id(auth0Id);
    if (maybeEmployeeUser.isPresent()) {
      log.info("Employee user found with auth0 ID: {}", auth0Id);
    } else {
      log.warn("No employee user found with auth0 ID: {}", auth0Id);
    }
    return maybeEmployeeUser;
  }
}
