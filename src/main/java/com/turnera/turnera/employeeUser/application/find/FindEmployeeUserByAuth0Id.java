package com.turnera.turnera.employeeUser.application.find;

import com.turnera.turnera.employeeUser.domain.EmployeeUserService;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindEmployeeUserByAuth0Id {

  private final EmployeeUserService employeeUserService;

  @Transactional
  public EmployeeUser find(String auth0Id) {
    return employeeUserService.findByAuth0Id(auth0Id);
  }
}
