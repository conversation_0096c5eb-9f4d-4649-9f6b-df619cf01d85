package com.turnera.turnera.employeeUser.application.find;

import com.turnera.turnera.employeeUser.domain.EmployeeUserService;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindEmployeeUserById {

  private final EmployeeUserService employeeUserService;

  public EmployeeUser find(Integer userId) {
    return employeeUserService.findById(userId);
  }
}
