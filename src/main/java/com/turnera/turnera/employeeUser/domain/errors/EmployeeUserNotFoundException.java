package com.turnera.turnera.employeeUser.domain.errors;

import com.turnera.turnera.configuration.exceptions.NotFoundException;

public class EmployeeUserNotFoundException extends NotFoundException {
  public EmployeeUserNotFoundException(Integer userId) {
    super("Employee user not found with ID: " + userId);
  }

  public EmployeeUserNotFoundException(String auth0Id) {
    super("Employee user not found with auth 0 ID: " + auth0Id);
  }
}
