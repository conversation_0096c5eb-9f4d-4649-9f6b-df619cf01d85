package com.turnera.turnera.employeeUser.infrastructure.entities;

import com.turnera.turnera.employeeUser.domain.entities.EmployeeUserType;
import com.turnera.turnera.employeeUser.presentation.entities.response.MedicalCenterInformationResponse;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "employee_user_medical_center_relationship")
public class EmployeeUserMedicalCenterRelationship {
  @SequenceGenerator(
      name = "employee_user_medical_center_relationship_id_gen",
      sequenceName = "employee_user_id_seq",
      allocationSize = 1)
  @EmbeddedId
  private EmployeeUserMedicalCenterRelationshipId id;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private EmployeeUserType type;

  @ManyToOne(fetch = FetchType.LAZY)
  @MapsId("medicalCenterId")
  @JoinColumn(name = "medical_center_id")
  private MedicalCenter medicalCenter;

  @ManyToOne(fetch = FetchType.LAZY)
  @MapsId("employeeUserId")
  @JoinColumn(name = "employee_user_id")
  private EmployeeUser employeeUser;

  public MedicalCenterInformationResponse mapToResponse() {
    return new MedicalCenterInformationResponse(
        getMedicalCenter().getId(),
        getMedicalCenter().getAddress(),
        getMedicalCenter().getName(),
        getMedicalCenter().getPhone(),
        getMedicalCenter().getDoctorsCount(),
        getType(),
        getMedicalCenter().getMedicalCenterSchedules().stream()
            .map(MedicalCenterSchedule::toScheduleDto)
            .toList());
  }
}
