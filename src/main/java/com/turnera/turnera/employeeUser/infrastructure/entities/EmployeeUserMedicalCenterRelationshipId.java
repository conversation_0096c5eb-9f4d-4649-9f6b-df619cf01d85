package com.turnera.turnera.employeeUser.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

@Getter
@Setter
@Embeddable
public class EmployeeUserMedicalCenterRelationshipId implements java.io.Serializable {
  private static final long serialVersionUID = 8296890285511458643L;

  @Column(name = "employee_user_id")
  private Integer employeeUserId;

  @Column(name = "medical_center_id")
  private Integer medicalCenterId;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    EmployeeUserMedicalCenterRelationshipId entity = (EmployeeUserMedicalCenterRelationshipId) o;
    return Objects.equals(this.medicalCenterId, entity.medicalCenterId)
        && Objects.equals(this.employeeUserId, entity.employeeUserId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(medicalCenterId, employeeUserId);
  }
}
