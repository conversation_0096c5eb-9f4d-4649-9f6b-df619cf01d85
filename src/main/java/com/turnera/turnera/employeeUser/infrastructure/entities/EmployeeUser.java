package com.turnera.turnera.employeeUser.infrastructure.entities;

import static com.turnera.turnera.utils.UserType.EMPLOYEE_USER;

import com.turnera.turnera.employeeUser.presentation.entities.response.EmployeeInitialInformationResponse;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "employee_user")
public class EmployeeUser extends User {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "employee_user_id_gen")
  @SequenceGenerator(
      name = "employee_user_id_gen",
      sequenceName = "employee_user_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @OneToMany(mappedBy = "employeeUser", fetch = FetchType.LAZY)
  private Set<EmployeeUserMedicalCenterRelationship> employeeUserMedicalCenterRelationships =
      new LinkedHashSet<>();

  @Column(name = "auth0_id", nullable = false, unique = true)
  private String auth0Id;

  public MedicalCenter getMedicalCenter(Integer medicalCenterId) {
    return employeeUserMedicalCenterRelationships.stream()
        .filter(relationship -> relationship.getId().getMedicalCenterId().equals(medicalCenterId))
        .map(EmployeeUserMedicalCenterRelationship::getMedicalCenter)
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "Medical center with ID "
                        + medicalCenterId
                        + " not found for employee user with ID "
                        + this.getId()));
  }

  public UserType getUserType() {
    return EMPLOYEE_USER;
  }

  @Override
  public Boolean isMedicalCenterSide() {
    return true;
  }

  public EmployeeInitialInformationResponse toEmployeeInitialInformationResponse() {
    return new EmployeeInitialInformationResponse(
        this.getId(),
        this.getName(),
        this.getSurname(),
        this.employeeUserMedicalCenterRelationships.stream()
            .map(EmployeeUserMedicalCenterRelationship::mapToResponse)
            .toList());
  }
}
