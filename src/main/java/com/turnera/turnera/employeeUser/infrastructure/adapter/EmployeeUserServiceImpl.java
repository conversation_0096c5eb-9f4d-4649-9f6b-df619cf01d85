package com.turnera.turnera.employeeUser.infrastructure.adapter;

import com.turnera.turnera.employeeUser.domain.EmployeeUserService;
import com.turnera.turnera.employeeUser.domain.errors.EmployeeUserNotFoundException;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.employeeUser.infrastructure.repository.EmployeeUserRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class EmployeeUserServiceImpl implements EmployeeUserService {

  private final EmployeeUserRepository employeeUserRepository;

  @Override
  public EmployeeUser findById(Integer userId) {
    return employeeUserRepository.findByIdOptional(userId).orElse(null);
  }

  @Override
  public Optional<EmployeeUser> findMaybeByAuth0Id(String auth0Id) {
    return employeeUserRepository.findByAuth0Id(auth0Id);
  }

  @Override
  public EmployeeUser findByAuth0Id(String auth0Id) {
    return employeeUserRepository
        .findByAuth0Id(auth0Id)
        .orElseThrow(() -> new EmployeeUserNotFoundException(auth0Id));
  }
}
