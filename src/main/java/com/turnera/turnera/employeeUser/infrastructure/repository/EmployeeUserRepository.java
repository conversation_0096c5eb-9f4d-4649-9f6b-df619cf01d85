package com.turnera.turnera.employeeUser.infrastructure.repository;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;

@ApplicationScoped
public class EmployeeUserRepository implements PanacheRepositoryBase<EmployeeUser, Integer> {

  public Optional<EmployeeUser> findByAuth0Id(String auth0Id) {
    return find("auth0Id", auth0Id).firstResultOptional();
  }
}
