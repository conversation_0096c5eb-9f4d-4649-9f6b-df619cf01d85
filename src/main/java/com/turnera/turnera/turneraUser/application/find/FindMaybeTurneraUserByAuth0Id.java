package com.turnera.turnera.turneraUser.application.find;

import com.turnera.turnera.turneraUser.domain.TurneraUserService;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindMaybeTurneraUserByAuth0Id {

  private final TurneraUserService turneraUserService;

  public Optional<TurneraUser> find(String auth0Id) {
    log.info("Finding TurneraUser by Auth0 ID: {}", auth0Id);
    Optional<TurneraUser> maybeTurneraUser = turneraUserService.findMaybeByAuth0Id(auth0Id);
    if (maybeTurneraUser.isPresent()) {
      log.info("TurneraUser found with Auth0 ID: {}", auth0Id);
    } else {
      log.warn("No <PERSON>aUser found with Auth0 ID: {}", auth0Id);
    }
    return maybeTurneraUser;
  }
}
