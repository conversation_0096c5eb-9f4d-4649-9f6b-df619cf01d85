package com.turnera.turnera.turneraUser.application.find;

import com.turnera.turnera.turneraUser.domain.TurneraUserService;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindActiveTurneraUserById {

  private final TurneraUserService turneraUserService;

  public TurneraUser find(Integer userId) {
    return turneraUserService.findActiveById(userId);
  }
}
