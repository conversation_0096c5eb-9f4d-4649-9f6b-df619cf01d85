package com.turnera.turnera.turneraUser.application.validations;

import com.turnera.turnera.turneraUser.application.find.FindActiveTurneraUserById;
import com.turnera.turnera.turneraUser.domain.errors.UserNotFoundException;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class UserValidations {

  private final FindActiveTurneraUserById findActiveTurneraUserById;

  @Transactional
  public TurneraUser verifyUserExistsAndIsActive(Integer userId) throws UserNotFoundException {
    TurneraUser user = findActiveTurneraUserById.find(userId);
    if (user == null) {
      throw new UserNotFoundException("User not found with ID: " + userId);
    }
    return user;
  }
}
