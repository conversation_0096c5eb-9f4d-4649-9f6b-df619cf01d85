package com.turnera.turnera.turneraUser.infrastructure.adapter;

import static com.turnera.turnera.turneraUser.domain.entities.UserState.ACTIVE;

import com.turnera.turnera.turneraUser.domain.TurneraUserService;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import com.turnera.turnera.turneraUser.infrastructure.repository.TurneraUserRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class TurneraUserServiceImpl implements TurneraUserService {

  private final TurneraUserRepository turneraUserRepository;

  @Override
  public TurneraUser findActiveById(Integer userId) {
    return turneraUserRepository.findByIdAndState(userId, ACTIVE.toString());
  }

  @Override
  public Optional<TurneraUser> findMaybeByAuth0Id(String auth0Id) {
    return turneraUserRepository.findByAuth0Id(auth0Id);
  }
}
