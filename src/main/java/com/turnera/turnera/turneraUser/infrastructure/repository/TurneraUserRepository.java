package com.turnera.turnera.turneraUser.infrastructure.repository;

import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;

@ApplicationScoped
public class TurneraUserRepository implements PanacheRepositoryBase<TurneraUser, Integer> {

  public TurneraUser findByIdAndState(Integer id, String state) {
    return find("id = ?1 AND state = CAST(?2 as USER_STATE)", id, state).firstResult();
  }

  public Optional<TurneraUser> findByAuth0Id(String auth0Id) {
    return find("auth0Id", auth0Id).firstResultOptional();
  }
}
