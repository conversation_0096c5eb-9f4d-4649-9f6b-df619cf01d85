package com.turnera.turnera.turneraUser.infrastructure.entities;

import static com.turnera.turnera.utils.UserType.TURNERA_USER;

import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.turneraUser.domain.entities.UserState;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.user.presentation.entities.TurneraUserInformationDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "turnera_user")
public class TurneraUser extends User {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "turnera_user_id_gen")
  @SequenceGenerator(
      name = "turnera_user_id_gen",
      sequenceName = "turnera_user_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @Size(max = 100)
  @Column(name = "email", length = 100)
  private String email;

  @Size(max = 20)
  @Column(name = "phone", length = 20)
  private String phone;

  @Enumerated(EnumType.STRING)
  @Column(name = "state", columnDefinition = "USER_STATE", nullable = false)
  private UserState state;

  @Column(name = "created_at")
  private LocalDateTime createdAt;

  @Column(name = "updated_at")
  private LocalDateTime updatedAt;

  @Size(max = 255)
  @NotNull
  @Column(name = "auth0_id", nullable = false)
  private String auth0Id;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private Set<Patient> patients = new LinkedHashSet<>();

  @PrePersist
  protected void onCreate() {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    createdAt = now;
    updatedAt = now;
  }

  @PreUpdate
  protected void onUpdate() {
    updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  @Override
  public UserType getUserType() {
    return TURNERA_USER;
  }

  @Override
  public Boolean isMedicalCenterSide() {
    return false;
  }

  public TurneraUserInformationDTO toResponse() {
    return new TurneraUserInformationDTO(email, phone, state);
  }
}
