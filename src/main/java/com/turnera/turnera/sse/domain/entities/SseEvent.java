package com.turnera.turnera.sse.domain.entities;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.ws.rs.sse.OutboundSseEvent;
import jakarta.ws.rs.sse.Sse;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class SseEvent {
  private String eventType;

  @Getter(AccessLevel.NONE)
  private JsonNode data;

  private Long timestamp;

  public String getData() {
    return data.toString();
  }

  public OutboundSseEvent toSendableEvent(Sse sse) {
    return sse.newEventBuilder().name(getEventType()).data(getData()).build();
  }
}
