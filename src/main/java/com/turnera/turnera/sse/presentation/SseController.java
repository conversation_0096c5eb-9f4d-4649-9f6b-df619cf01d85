package com.turnera.turnera.sse.presentation;

import com.turnera.turnera.sse.application.medicalCenter.SseMedicalCenterEvents;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.sse.Sse;
import jakarta.ws.rs.sse.SseEventSink;
import lombok.RequiredArgsConstructor;

@Path("/sse")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.TEXT_PLAIN)
public class SseController {

  private final SseMedicalCenterEvents sseMedicalCenterEvents;

  @GET
  @Path("/data/{medical-center-id}")
  @Produces(MediaType.SERVER_SENT_EVENTS)
  public void streamEvents(
      @PathParam("medical-center-id") Integer medicalCenterId,
      @QueryParam("employee-user-id") Integer employeeUserId,
      @Context SseEventSink sseEventSink,
      @Context Sse sse) {
    sseMedicalCenterEvents.createEventSink(medicalCenterId, sseEventSink);
  }
}
