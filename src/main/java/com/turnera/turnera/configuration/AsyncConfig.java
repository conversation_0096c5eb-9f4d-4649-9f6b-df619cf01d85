package com.turnera.turnera.configuration;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@ApplicationScoped
public class AsyncConfig {

  @Produces
  @ApplicationScoped
  @SseTaskScheduler
  public ScheduledExecutorService taskScheduler() {
    return Executors.newScheduledThreadPool(
        10,
        r -> {
          Thread t = new Thread(r);
          t.setName("sse-heartbeat-" + t.threadId());
          t.setDaemon(true);
          return t;
        });
  }
}
