package com.turnera.turnera.configuration;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerResponseContext;
import jakarta.ws.rs.container.ContainerResponseFilter;
import jakarta.ws.rs.ext.Provider;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@ApplicationScoped
public class SecurityConfig {

  void onStart(@Observes StartupEvent ev) {
    // Application startup configuration if needed
    // In Quarkus, most security configuration is done via application.properties
  }

  @Provider
  @ApplicationScoped
  public static class CorsFilter implements ContainerResponseFilter {

    private static final List<String> ALLOWED_ORIGINS =
        Arrays.asList(
            "localhost:3000",
            "http://localhost:3000",
            "https://localhost:3000",
            "localhost:8080",
            "http://localhost:8080",
            "https://localhost:8080",
            "http://dying-lindy-turnera-cf8cc166.koyeb.app",
            "https://dying-lindy-turnera-cf8cc166.koyeb.app",
            "*");

    @Override
    public void filter(
        ContainerRequestContext requestContext, ContainerResponseContext responseContext)
        throws IOException {

      String origin = requestContext.getHeaderString("Origin");

      // Set CORS headers
      if (origin != null && (ALLOWED_ORIGINS.contains("*") || ALLOWED_ORIGINS.contains(origin))) {
        responseContext.getHeaders().add("Access-Control-Allow-Origin", origin);
      } else if (ALLOWED_ORIGINS.contains("*")) {
        responseContext.getHeaders().add("Access-Control-Allow-Origin", "*");
      }

      responseContext
          .getHeaders()
          .add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH");
      responseContext
          .getHeaders()
          .add(
              "Access-Control-Allow-Headers",
              "Origin, Content-Type, Accept, Authorization, X-Requested-With, Access-Control-Request-Method, Access-Control-Request-Headers");
      responseContext.getHeaders().add("Access-Control-Allow-Credentials", "true");
      responseContext.getHeaders().add("Access-Control-Max-Age", "3600");
    }
  }
}
