package com.turnera.turnera.configuration;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import org.eclipse.microprofile.openapi.OASFactory;
import org.eclipse.microprofile.openapi.models.OpenAPI;
import org.eclipse.microprofile.openapi.models.security.SecurityScheme;

@ApplicationScoped
public class SwaggerConfig {

  @Produces
  @ApplicationScoped
  public OpenAPI customOpenAPI() {
    return OASFactory.createOpenAPI()
        .info(OASFactory.createInfo().title("JavaInUse Authentication Service").version("1.0.0"))
        .addSecurityRequirement(
            OASFactory.createSecurityRequirement().addScheme("JavaInUseSecurityScheme"))
        .components(
            OASFactory.createComponents()
                .addSecurityScheme(
                    "JavaInUseSecurityScheme",
                    OASFactory.createSecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")));
  }
}
