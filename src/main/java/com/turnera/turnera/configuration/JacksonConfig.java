package com.turnera.turnera.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.turnera.turnera.utils.deserializers.CustomDateDeserializer;
import com.turnera.turnera.utils.deserializers.CustomTimeDeserializer;
import io.quarkus.jackson.ObjectMapperCustomizer;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.time.LocalDate;
import java.time.LocalTime;

@ApplicationScoped
public class JacksonConfig implements ObjectMapperCustomizer {

  @Inject CustomDateDeserializer customDateDeserializer;

  @Inject CustomTimeDeserializer customTimeDeserializer;

  @Override
  public void customize(ObjectMapper objectMapper) {
    SimpleModule module = new SimpleModule();
    module.addDeserializer(LocalDate.class, customDateDeserializer);
    module.addDeserializer(LocalTime.class, customTimeDeserializer);
    objectMapper.registerModule(module);
  }
}
