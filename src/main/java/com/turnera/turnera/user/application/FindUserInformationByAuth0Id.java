package com.turnera.turnera.user.application;

import com.turnera.turnera.employeeUser.application.find.FindMaybeEmployeeUserByAuth0Id;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUserMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.presentation.entities.response.MedicalCenterInformationResponse;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.application.find.FindMaybeProfessionalByAuth0Id;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.turneraUser.application.find.FindMaybeTurneraUserByAuth0Id;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.user.presentation.entities.TurneraPatientInformationDTO;
import com.turnera.turnera.user.presentation.entities.UserDTO;
import com.turnera.turnera.utils.UserType;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindUserInformationByAuth0Id {

  private final FindMaybeTurneraUserByAuth0Id findMaybeTurneraUserByAuth0Id;
  private final FindMaybeProfessionalByAuth0Id findMaybeProfessionalByAuth0Id;
  private final FindMaybeEmployeeUserByAuth0Id findMaybeEmployeeUserByAuth0Id;

  @Transactional
  public UserDTO find(String auth0Id) {
    log.info("Finding user information by Auth0 ID: {}", auth0Id);
    Optional<TurneraUser> maybeTurneraUser = findMaybeTurneraUserByAuth0Id.find(auth0Id);
    Optional<EmployeeUser> maybeEmployeeUser = findMaybeEmployeeUserByAuth0Id.find(auth0Id);
    Optional<Professional> maybeProfessional = findMaybeProfessionalByAuth0Id.find(auth0Id);
    Map<UserType, Integer> idFromRole = new HashMap<>();
    String name = getName(maybeTurneraUser, maybeEmployeeUser, maybeProfessional);
    String surname = getSurname(maybeTurneraUser, maybeEmployeeUser, maybeProfessional);
    List<UserType> roles = new ArrayList<>();
    setIdAndRoleIfPresent(idFromRole, roles, maybeTurneraUser);
    setIdAndRoleIfPresent(idFromRole, roles, maybeEmployeeUser);
    setIdAndRoleIfPresent(idFromRole, roles, maybeProfessional);
    List<MedicalCenterInformationResponse> medicalCenters =
        maybeEmployeeUser
            .map(
                employeeUser ->
                    employeeUser.getEmployeeUserMedicalCenterRelationships().stream()
                        .map(EmployeeUserMedicalCenterRelationship::mapToResponse)
                        .toList())
            .orElse(null);
    List<TurneraPatientInformationDTO> turneraPatientInformationDTO =
        maybeTurneraUser
            .map(user -> user.getPatients().stream().map(Patient::toResponse).toList())
            .orElse(null);
    log.info("User information found");
    return new UserDTO(
        idFromRole,
        name,
        surname,
        roles,
        medicalCenters,
        maybeTurneraUser.map(TurneraUser::toResponse).orElse(null),
        turneraPatientInformationDTO,
        maybeProfessional.map(Professional::toResponse).orElse(null),
        auth0Id);
  }

  private void setIdAndRoleIfPresent(
      Map<UserType, Integer> idFromRole, List<UserType> roles, Optional<? extends User> maybeUser) {
    maybeUser.ifPresent(
        user -> {
          idFromRole.put(user.getUserType(), user.getId());
          roles.add(user.getUserType());
        });
  }

  private String getName(
      Optional<TurneraUser> maybeTurneraUser,
      Optional<EmployeeUser> maybeEmployeeUser,
      Optional<Professional> maybeProfessional) {
    return maybeTurneraUser
        .map(User::getName)
        .orElse(
            maybeEmployeeUser
                .map(User::getName)
                .orElse(maybeProfessional.map(User::getName).orElse("")));
  }

  private String getSurname(
      Optional<TurneraUser> maybeTurneraUser,
      Optional<EmployeeUser> maybeEmployeeUser,
      Optional<Professional> maybeProfessional) {
    return maybeTurneraUser
        .map(User::getSurname)
        .orElse(
            maybeEmployeeUser
                .map(User::getSurname)
                .orElse(maybeProfessional.map(User::getSurname).orElse("")));
  }
}
