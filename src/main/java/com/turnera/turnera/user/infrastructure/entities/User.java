package com.turnera.turnera.user.infrastructure.entities;

import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
public abstract class User {

  @Size(max = 255)
  @NotNull
  @Column(name = "name", nullable = false)
  private String name;

  @Size(max = 255)
  @NotNull
  @Column(name = "surname", nullable = false)
  private String surname;

  public abstract UserType getUserType();

  public abstract Boolean isMedicalCenterSide();

  public abstract Integer getId();
}
