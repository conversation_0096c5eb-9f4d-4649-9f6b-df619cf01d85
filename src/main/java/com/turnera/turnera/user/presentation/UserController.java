package com.turnera.turnera.user.presentation;

import com.turnera.turnera.user.application.FindUserInformationByAuth0Id;
import com.turnera.turnera.user.presentation.entities.UserDTO;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;

@Path("/user")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
public class UserController {

  private final FindUserInformationByAuth0Id findUserInformationByAuth0Id;

  @GET
  @Path("/{auth0Id}")
  public UserDTO getEmployeeUserByAuth0Id(@PathParam("auth0Id") String auth0Id) {
    return findUserInformationByAuth0Id.find(auth0Id);
  }
}
