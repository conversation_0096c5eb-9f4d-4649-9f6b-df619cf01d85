package com.turnera.turnera.user.presentation.entities;

import com.turnera.turnera.turneraUser.domain.entities.UserState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TurneraUserInformationDTO {
  private String email;
  private String phone;
  private UserState state;
}
