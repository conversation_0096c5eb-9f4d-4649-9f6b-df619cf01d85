package com.turnera.turnera.user.presentation.entities;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TurneraPatientInformationDTO {
  private Integer id;
  private String name;
  private String surname;
  private String identificationNumber;
  private String phone;
  private String email;
  private BigDecimal attendancePercentage;
  private LocalDate dateOfBirth;
  private String plan;
}
