package com.turnera.turnera.user.presentation.entities;

import com.turnera.turnera.employeeUser.presentation.entities.response.MedicalCenterInformationResponse;
import com.turnera.turnera.utils.UserType;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
  private Map<UserType, Integer> idFromRole;
  private String name;
  private String surname;
  private List<UserType> roles;
  private List<MedicalCenterInformationResponse> medicalCenters;
  private TurneraUserInformationDTO turneraUserInformation;
  private List<TurneraPatientInformationDTO> turneraPatientInformation;
  private TurneraProfessionalInformationDTO turneraProfessionalInformation;
  private String auth0Sub;
}
