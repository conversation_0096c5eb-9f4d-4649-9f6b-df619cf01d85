package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.NotFoundException;

import java.time.DayOfWeek;

public class AppointmentScheduleNotFoundException extends NotFoundException {

  public AppointmentScheduleNotFoundException(
      Integer medicalCenterId, Integer professionalId, DayOfWeek weekDay) {
    super(
        ("No appointment schedule found for medical center %d, professional %d and week day %s")
            .formatted(medicalCenterId, professionalId, weekDay));
  }
}
