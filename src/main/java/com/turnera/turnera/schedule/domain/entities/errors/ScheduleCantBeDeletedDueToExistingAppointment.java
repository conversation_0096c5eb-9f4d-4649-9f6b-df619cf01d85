package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;

import java.util.List;

public class ScheduleCantBeDeletedDueToExistingAppointment extends ConflictException {

    public ScheduleCantBeDeletedDueToExistingAppointment(Integer scheduleId, List<Integer> appointmentIds) {
        super(("Cannot delete schedule with ID %d because of existing appointments %s that rely on it")
                .formatted(scheduleId, appointmentIds.toString()));
    }
}