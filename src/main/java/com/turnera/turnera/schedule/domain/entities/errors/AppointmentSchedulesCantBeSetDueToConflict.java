package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.configuration.exceptions.ConflictException;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import java.time.DayOfWeek;
import java.util.List;

public class AppointmentSchedulesCantBeSetDueToConflict extends ConflictException {

  public AppointmentSchedulesCantBeSetDueToConflict(
      Integer medicalCenterId,
      Integer professionalId,
      List<Appointment> conflictingAppointments,
      DayOfWeek dayOfWeek) {
    super(
        ("Cannot set appointment schedules for medical center %s and professional %s for day %s "
                + "because there are conflicts with %d appointments")
            .formatted(medicalCenterId, professionalId, dayOfWeek, conflictingAppointments.size()));
  }

  public AppointmentSchedulesCantBeSetDueToConflict(
      Integer medicalCenterId,
      Integer professionalId,
      AppointmentScheduleDayInputData firstSchedule,
      AppointmentScheduleDayInputData secondSchedule) {
    super(
        ("Cannot create appointment schedules for medical center %s and professional %s for day %s "
                + "because there is a conflict between schedule from %s to %s and schedule from %s to %s")
            .formatted(
                medicalCenterId,
                professionalId,
                firstSchedule.getDayOfWeek(),
                firstSchedule.getStartTime(),
                firstSchedule.getEndTime(),
                secondSchedule.getStartTime(),
                secondSchedule.getEndTime()));
  }

  public AppointmentSchedulesCantBeSetDueToConflict(
      Integer medicalCenterId, Integer professionalId, DayOfWeek dayOfWeek) {
    super(
        "Cannot create appointment schedules for medical center %s and professional %s for day %s because it doesn't fit any medical center schedules"
            .formatted(medicalCenterId, professionalId, dayOfWeek));
  }
}
