package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.presentation.entities.bodies.AppointmentScheduleDayBody;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentScheduleCreationInput {

  private EmployeeUser employeeUser;

  private MedicalCenter medicalCenter;

  private Integer professionalId;

  private List<AppointmentScheduleDayInputData> days;

  public AppointmentScheduleCreationInput(
      EmployeeUser employeeUser,
      Integer medicalCenterId,
      Integer professionalId,
      List<AppointmentScheduleDayBody> days) {
    this.employeeUser = employeeUser;
    this.medicalCenter = employeeUser.getMedicalCenter(medicalCenterId);
    this.professionalId = professionalId;
    this.days = days.stream().map(AppointmentScheduleDayBody::toDomain).toList();
  }

  public Integer getMedicalCenterId() {
    return medicalCenter.getId();
  }
}
