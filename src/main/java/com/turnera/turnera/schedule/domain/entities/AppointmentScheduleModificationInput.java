package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.schedule.presentation.entities.bodies.AppointmentScheduleModificationBody;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AppointmentScheduleModificationInput {


    private EmployeeUser employeeUser;

    private Integer professionalId;

    private List<AppointmentScheduleModificationBody> days;

    public AppointmentScheduleModificationInput(EmployeeUser employeeUser, Integer professionalId, List<AppointmentScheduleModificationBody> days) {
        this.employeeUser = employeeUser;
        this.professionalId = professionalId;
        this.days = days;
    }

}