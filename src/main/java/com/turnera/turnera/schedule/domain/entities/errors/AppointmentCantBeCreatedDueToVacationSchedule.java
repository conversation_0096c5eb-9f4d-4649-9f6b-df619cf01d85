package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;

public class AppointmentCantBeCreatedDueToVacationSchedule extends ConflictException {

  public AppointmentCantBeCreatedDueToVacationSchedule(Integer vacationScheduleId) {
    super(
        "Cannot create appointment because the professional is on vacation "
            + vacationScheduleId
            + " during the requested date");
  }
}
