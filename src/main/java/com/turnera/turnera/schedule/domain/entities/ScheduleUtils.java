package com.turnera.turnera.schedule.domain.entities;

import static com.turnera.turnera.utils.LocalTimeUtils.multiplyLocalTime;

import java.time.LocalTime;

public class ScheduleUtils {

  /**
   * Checks if an appointment fits within a schedule's time range.
   *
   * @param scheduleStartTime The schedule's start time
   * @param scheduleEndTime The schedule's end time
   * @param scheduleIntervalTime The interval time between appointments in the schedule
   * @param appointmentStartTime The start time of the appointment to check
   * @param appointmentIntervalAmount The number of appointment intervals
   * @return true if the appointment fits within the schedule's time range, false otherwise
   */
  public static boolean isInTimeRangeAndAppointmentStartTimeFits(
      LocalTime scheduleStartTime,
      LocalTime scheduleEndTime,
      LocalTime scheduleIntervalTime,
      LocalTime appointmentStartTime,
      Integer appointmentIntervalAmount) {
    LocalTime appointmentDuration =
        multiplyLocalTime(scheduleIntervalTime, appointmentIntervalAmount);
    LocalTime appointmentEndTime =
        appointmentStartTime.plusSeconds(appointmentDuration.toSecondOfDay());
    // Check if the appointment starts after or at the schedule start and ends before or at the
    // schedule end
    boolean startsAfterOrAtScheduleStart = !appointmentStartTime.isBefore(scheduleStartTime);
    boolean endsBeforeOrAtScheduleEnd = !appointmentEndTime.isAfter(scheduleEndTime);
    // Check if the appointmentStartTime is a sum of scheduleStartTime + a multiple of
    // scheduleIntervalTime
    boolean isStartTimeValid =
        isMultipleOfInterval(scheduleStartTime, appointmentStartTime, scheduleIntervalTime);
    return startsAfterOrAtScheduleStart && endsBeforeOrAtScheduleEnd && isStartTimeValid;
  }

  private static boolean isMultipleOfInterval(
      LocalTime startTime, LocalTime appointmentTime, LocalTime intervalTime) {
    // Calculate the difference in seconds between the appointment time and the start time
    long startTimeSeconds = startTime.toSecondOfDay();
    long appointmentTimeSeconds = appointmentTime.toSecondOfDay();
    long intervalTimeSeconds = intervalTime.toSecondOfDay();

    // Calculate the difference in seconds
    long difference = appointmentTimeSeconds - startTimeSeconds;

    // Check if the difference is a multiple of the interval time
    return difference >= 0 && difference % intervalTimeSeconds == 0;
  }
}
