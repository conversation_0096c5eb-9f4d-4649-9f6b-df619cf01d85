package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;

public class MedicalCenterSchedulesCantBeDeletedDueToConflict extends ConflictException {

  public MedicalCenterSchedulesCantBeDeletedDueToConflict(
      Integer medicalCenterId, Integer appointmentScheduleId) {
    super(
        ("Cannot delete medical center schedules for medical center %s because it conflicts with appointment schedule %s. Please remove the conflicting schedules and try again.")
            .formatted(medicalCenterId, appointmentScheduleId));
  }
}
