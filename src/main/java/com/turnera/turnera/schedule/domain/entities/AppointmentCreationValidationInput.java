package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentCreationValidationInput {
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;
  private LocalDate date;
  private LocalTime startTime;
  private Integer appointmentSlotDuration;
  private Boolean isMedicalCenterSideAgenda;

  public AppointmentCreationValidationInput(
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship,
      LocalDate date,
      LocalTime startTime,
      Integer appointmentSlotDuration,
      Boolean isMedicalCenterSideAgenda) {
    this.professionalMedicalCenterRelationship = professionalMedicalCenterRelationship;
    this.date = date;
    this.startTime = startTime;
    this.appointmentSlotDuration = appointmentSlotDuration;
    this.isMedicalCenterSideAgenda = isMedicalCenterSideAgenda;
  }
}
