package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VacationScheduleCreationInput {

  private EmployeeUser employeeUser;
  private Integer medicalCenterId;
  private Integer professionalId;
  private List<VacationScheduleDayBody> days;

  public VacationScheduleCreationInput(
      EmployeeUser employeeUser,
      Integer medicalCenterId,
      Integer professionalId,
      List<VacationScheduleDayBody> days) {
    this.employeeUser = employeeUser;
    this.medicalCenterId = medicalCenterId;
    this.professionalId = professionalId;
    this.days = days;
  }
}
