package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.configuration.exceptions.ConflictException;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;

public class VacationSchedulesCantBeCreatedDueToConflict extends ConflictException {

    public VacationSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId,
                                                       Integer professionalId,
                                                       VacationScheduleDayBody vacationSchedule,
                                                       VacationSchedule oldVacationSchedule) {
        super(
                ("Cannot create vacation schedule for medical center %s and professional %s from date %s to date %s " +
                        "because there is a conflict with an existing vacation schedule %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, vacationSchedule.getFromDate(), vacationSchedule.getToDate(), oldVacationSchedule.getId()
                )
        );
    }

    public VacationSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId, Integer professionalId, VacationScheduleDayBody day, SpecialSchedule specialSchedule) {
        super(
                ("Cannot create vacation schedule for medical center %s and professional %s from date %s to date %s " +
                        "because there is a conflict with an existing special schedule %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, day.getFromDate(), day.getToDate(), specialSchedule.getId()
                )
        );
    }

    public VacationSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId, Integer professionalId, VacationScheduleDayBody day, Appointment appointment) {
        super(
                ("Cannot create vacation schedule for medical center %s and professional %s from date %s to date %s " +
                        "because there is a conflict with an existing appointment %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, day.getFromDate(), day.getToDate(), appointment.getId()
                )
        );
    }
}