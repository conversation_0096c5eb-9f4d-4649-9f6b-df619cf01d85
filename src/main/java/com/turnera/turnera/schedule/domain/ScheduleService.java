package com.turnera.turnera.schedule.domain;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.domain.entities.*;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface ScheduleService {

  void createVacationSchedule(VacationScheduleCreationInput input, VacationScheduleDayBody day);

  void createSpecialSchedule(SpecialScheduleCreationInput input, SpecialScheduleDayBody day);

  void createAppointmentSchedule(
      AppointmentScheduleCreationInput input, AppointmentScheduleDayInputData day);

  List<AppointmentSchedule> findAppointmentSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId);

  List<AppointmentSchedule> findAppointmentSchedulesByMedicalCenterIdAndProfessionalIdAndWeekDays(
      Integer medicalCenterId, Integer professionalId, List<DayOfWeek> weekDays);

  AppointmentSchedule findAppointmentScheduleByMedicalCenterIdAndProfessionalIdAndWeekDay(
      Integer medicalCenterId, Integer professionalId, DayOfWeek weekDay);

  List<VacationSchedule> findVacationSchedulesByMedicalCenterIdAndProfessionalIdAfterDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date);

  List<VacationSchedule> findVacationSchedulesByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
      Integer medicalCenterId, Integer professionalId, List<LocalDate> dates);

  List<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDateIn(
      Integer medicalCenterId, Integer professionalId, List<LocalDate> date);

  Optional<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date);

  List<AppointmentSchedule> findAllAppointmentSchedulesByIds(List<Integer> integers);

  void deleteAppointmentSchedules(List<AppointmentSchedule> schedules);

  List<SpecialSchedule> findAllSpecialSchedulesByIds(List<Integer> integers);

  void deleteSpecialSchedules(List<SpecialSchedule> schedules);

  List<VacationSchedule> findALlVacationSchedulesByIds(List<Integer> integers);

  void deleteVacationSchedules(List<VacationSchedule> schedules);

  void updateAppointmentSchedule(
      AppointmentSchedule scheduleToModify, AppointmentScheduleDayInputData modificationData);

  List<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId);

  List<VacationSchedule> findVacationSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId);

  void createMedicalCenterSchedule(
      MedicalCenterScheduleDayInputData input,
      MedicalCenter medicalCenter,
      EmployeeUser employeeUser);

  void updateMedicalCenterSchedule(
      MedicalCenterSchedule medicalCenterScheduleToModify,
      MedicalCenterScheduleDayInputData modificationData,
      EmployeeUser employeeUser);

  void deleteMedicalCenterSchedules(List<MedicalCenterSchedule> medicalCenterSchedulesToDelete);
}
