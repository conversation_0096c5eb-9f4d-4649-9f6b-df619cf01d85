package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SpecialScheduleCreationInput {

  private EmployeeUser employeeUser;
  private MedicalCenter medicalCenter;
  private Integer professionalId;
  private List<SpecialScheduleDayBody> days;

  public SpecialScheduleCreationInput(
      EmployeeUser employeeUser,
      Integer medicalCenterId,
      Integer professionalId,
      List<SpecialScheduleDayBody> days) {
    this.employeeUser = employeeUser;
    this.medicalCenter = employeeUser.getMedicalCenter(medicalCenterId);
    this.professionalId = professionalId;
    this.days = days;
  }

  public Integer getMedicalCenterId() {
    return medicalCenter.getId();
  }
}
