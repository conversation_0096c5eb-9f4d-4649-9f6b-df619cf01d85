package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentScheduleDayInputData implements ScheduleForDay {

  private Integer id;

  private LocalDate startingAt;

  private Optional<LocalDate> endingAt;

  private LocalTime startTime;

  private LocalTime endTime;

  private DayOfWeek dayOfWeek;

  private Integer weeklyFrequency;

  public AppointmentScheduleDayInputData(
      Integer id,
      LocalDate startingAt,
      Optional<LocalDate> endingAt,
      LocalTime startTime,
      LocalTime endTime,
      DayOfWeek dayOfWeek,
      Integer weeklyFrequency) {
    this.id = id;
    this.startingAt = startingAt;
    this.endingAt = endingAt;
    this.startTime = startTime;
    this.endTime = endTime;
    this.dayOfWeek = dayOfWeek;
    this.weeklyFrequency = weeklyFrequency;
  }

  public Boolean conflictsWith(AppointmentScheduleDayInputData other) {
    if (!this.getDayOfWeek().equals(other.getDayOfWeek())) {
      return false;
    }
    if (this.getStartTime().isBefore(other.getStartTime())) {
      return this.getEndTime().isAfter(other.getStartTime());
    } else {
      return other.getEndTime().isAfter(this.getStartTime());
    }
  }

  public Boolean idIsDefined() {
    return this.id != null;
  }

  public AppointmentSchedule toAppointmentSchedule(
      EmployeeUser employeeUser, MedicalCenter medicalCenter, Professional professional) {
    return new AppointmentSchedule(
        employeeUser,
        medicalCenter,
        professional,
        dayOfWeek,
        weeklyFrequency,
        startTime,
        endTime,
        startingAt,
        endingAt);
  }
}
