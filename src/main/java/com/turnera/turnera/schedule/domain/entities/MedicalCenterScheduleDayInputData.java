package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import com.turnera.turnera.utils.BuenosAiresTime;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class MedicalCenterScheduleDayInputData {

  @Getter(AccessLevel.NONE)
  private Integer id;

  private LocalTime startTime;

  private LocalTime endTime;

  private DayOfWeek weekDay;

  public MedicalCenterScheduleDayInputData(
      Integer id, LocalTime startTime, LocalTime endTime, DayOfWeek weekDay) {
    this.id = id;
    this.startTime = startTime;
    this.endTime = endTime;
    this.weekDay = weekDay;
  }

  public Optional<Integer> getId() {
    return Optional.ofNullable(id);
  }

  public MedicalCenterSchedule toMedicalCenterSchedule(
      MedicalCenter medicalCenter, EmployeeUser employeeUser) {
    LocalDateTime creationTime = BuenosAiresTime.nowAsLocalDateTime();
    return new MedicalCenterSchedule(
        Integer.MAX_VALUE,
        medicalCenter,
        this.startTime,
        this.endTime,
        employeeUser, // createdBy will be set later
        creationTime,
        employeeUser,
        creationTime,
        this.weekDay);
  }
}
