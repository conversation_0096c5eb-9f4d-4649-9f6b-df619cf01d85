package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;

import java.util.List;

public class ScheduleCantBeDeletedDueToExistingAppointments extends ConflictException {

    public ScheduleCantBeDeletedDueToExistingAppointments(Integer scheduleId, List<Integer> appointmentIds) {
        super(("Cannot delete schedule with ID %d because there are existing appointments with IDs: %s")
                .formatted(scheduleId, appointmentIds.toString()));
    }
}