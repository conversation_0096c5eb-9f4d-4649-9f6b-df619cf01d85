package com.turnera.turnera.schedule.domain.entities;

import static com.turnera.turnera.utils.LocalTimeUtils.multiplyLocalTime;

import java.time.LocalDate;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SlotData {
  private LocalDate date;
  private LocalTime slotStartTime;
  private Integer slotDuration;

  public LocalTime getSlotEndTime(LocalTime appointmentIntervalTime) {
    LocalTime appointmentDuration = multiplyLocalTime(appointmentIntervalTime, getSlotDuration());
    return getSlotStartTime().plusSeconds(appointmentDuration.toSecondOfDay());
  }

  public boolean isMultiple() {
    return getSlotDuration() > 1;
  }
}
