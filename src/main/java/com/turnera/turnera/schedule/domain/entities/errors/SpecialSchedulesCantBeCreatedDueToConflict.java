package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;

public class SpecialSchedulesCantBeCreatedDueToConflict extends ConflictException {


    public SpecialSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId,
                                                      Integer professionalId,
                                                      SpecialScheduleDayBody specialSchedule,
                                                      AppointmentSchedule appointmentSchedule) {
        super(
                ("Cannot create special schedule for medical center %s and professional %s for date %s " +
                        "from %s to %s because there is a conflict with an existing appointment schedule %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, specialSchedule.getDate(), specialSchedule.getStartTime(), specialSchedule.getEndTime(), appointmentSchedule.getId()
                )
        );
    }

    public SpecialSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId,
                                                      Integer professionalId,
                                                      SpecialScheduleDayBody specialSchedule,
                                                      VacationSchedule vacationSchedule) {
        super(
                ("Cannot create special schedule for medical center %s and professional %s for date %s " +
                        "from %s to %s because there is a conflict with an existing vacation schedule %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, specialSchedule.getDate(), vacationSchedule.getFromDate(), vacationSchedule.getToDate(), vacationSchedule.getId()
                )
        );
    }

    public SpecialSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId, Integer professionalId, SpecialScheduleDayBody day, SpecialSchedule specialSchedule) {
        super(
                ("Cannot create special schedule for medical center %s and professional %s for date %s " +
                        "from %s to %s because there is a conflict with an existing special schedule %s for the same professional and medical center").formatted(
                        medicalCenterId, professionalId, day.getDate(), day.getStartTime(), day.getEndTime(), specialSchedule.getId()
                )
        );
    }

    public SpecialSchedulesCantBeCreatedDueToConflict(Integer medicalCenterId, Integer professionalId, SpecialScheduleDayBody current, SpecialScheduleDayBody other) {
        super("Conflict detected between new special schedules - Day: %s, First schedule: %s-%s, Second schedule: %s-%s".formatted(
                current.getDate(), current.getStartTime(), current.getEndTime(), other.getStartTime(), other.getEndTime()
        ));
    }
}