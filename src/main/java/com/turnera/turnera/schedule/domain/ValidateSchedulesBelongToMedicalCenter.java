package com.turnera.turnera.schedule.domain;

import com.turnera.turnera.schedule.domain.entities.errors.ScheduleDoesntBelongToMedicalCenter;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class ValidateSchedulesBelongToMedicalCenter {

  public void validateAppointmentSchedules(
      List<AppointmentSchedule> schedules, Integer medicalCenterId) {
    schedules.forEach(
        schedule -> {
          if (!schedule.belongsToMedicalCenter(medicalCenterId)) {
            throwErrorForSchedule(schedule.getId(), medicalCenterId);
          }
        });
  }

  public void validateSpecialSchedules(List<SpecialSchedule> schedules, Integer medicalCenterId) {
    schedules.forEach(
        schedule -> {
          if (!schedule.belongsToMedicalCenter(medicalCenterId)) {
            throwErrorForSchedule(schedule.getId(), medicalCenterId);
          }
        });
  }

  public void validateVacationSchedules(List<VacationSchedule> schedules, Integer medicalCenterId) {
    schedules.forEach(
        schedule -> {
          if (!schedule.belongsToMedicalCenter(medicalCenterId)) {
            throwErrorForSchedule(schedule.getId(), medicalCenterId);
          }
        });
  }

  private void throwErrorForSchedule(Integer id, Integer medicalCenterId) {
    log.error("Schedule {} doesn't belong to {}", id, medicalCenterId);
    throw new ScheduleDoesntBelongToMedicalCenter(id, medicalCenterId);
  }
}
