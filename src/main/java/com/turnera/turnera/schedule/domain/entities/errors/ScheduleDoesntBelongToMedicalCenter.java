package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class ScheduleDoesntBelongToMedicalCenter extends BadRequestException {

    public ScheduleDoesntBelongToMedicalCenter(Integer scheduleId, Integer medicalCenterId) {
        super(("Schedule %d doesn't belong to %d").formatted(scheduleId, medicalCenterId));

    }
}