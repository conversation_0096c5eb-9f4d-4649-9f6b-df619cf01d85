package com.turnera.turnera.schedule.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class OverlappingAppointmentSum {
  private Integer overlappingEndingAppointments;
  private Integer overlappingStartingAppointments;

  public int highestOverlap() {
    return Integer.max(overlappingEndingAppointments, overlappingStartingAppointments);
  }
}
