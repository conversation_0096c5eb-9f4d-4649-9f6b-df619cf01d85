package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class MedicalCenterScheduleCreationInput {

  private EmployeeUser employeeUser;
  private MedicalCenter medicalCenter;
  private List<MedicalCenterScheduleDayInputData> days;

  public MedicalCenterScheduleCreationInput(
      EmployeeUser employeeUser,
      Integer medicalCenterId,
      List<MedicalCenterScheduleDayInputData> days) {
    this.employeeUser = employeeUser;
    this.medicalCenter = employeeUser.getMedicalCenter(medicalCenterId);
    this.days = days;
  }
}
