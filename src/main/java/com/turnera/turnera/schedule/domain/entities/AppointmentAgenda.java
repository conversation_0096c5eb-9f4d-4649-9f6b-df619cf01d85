package com.turnera.turnera.schedule.domain.entities;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.professional.domain.entities.OverlappedAppointmentLimit;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.entities.errors.AppointmentCantBeCreatedDueToNoAvailableSlots;
import com.turnera.turnera.schedule.domain.entities.errors.AppointmentCantBeCreatedDueToVacationSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.utils.databaseUtils.slot.Slot;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AppointmentAgenda {

  // TODO:  Appointments should be of same date validation but still doing filters , check for
  // performance in production
  private List<Appointment> appointments;

  private List<AppointmentSchedule> appointmentSchedules;

  private List<SpecialSchedule> specialSchedules;

  private List<VacationSchedule> vacationSchedules;

  private List<BlockedSlot> blockedSlots;
  private OverlappedAppointmentLimit overlappedAppointmentLimit;
  private Boolean isMedicalCenterSideAgenda;
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  public AppointmentAgenda(
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship,
      List<Appointment> appointments,
      List<AppointmentSchedule> appointmentSchedules,
      List<SpecialSchedule> specialSchedules,
      List<VacationSchedule> vacationSchedules,
      List<BlockedSlot> blockedSlots,
      OverlappedAppointmentLimit overlappedAppointmentLimit,
      Boolean isMedicalCenterSideAgenda) {
    this.professionalMedicalCenterRelationship = professionalMedicalCenterRelationship;
    this.appointments = appointments;
    this.appointmentSchedules = appointmentSchedules;
    this.specialSchedules = specialSchedules;
    this.vacationSchedules = vacationSchedules;
    this.blockedSlots = blockedSlots;
    this.overlappedAppointmentLimit = overlappedAppointmentLimit;
    this.isMedicalCenterSideAgenda = isMedicalCenterSideAgenda;
  }

  private List<BlockedSlot> patientBlockedSlots() {
    return blockedSlots.stream().filter(BlockedSlot::isPatientSide).toList();
  }

  private List<BlockedSlot> doctorBlockedSlots() {
    return blockedSlots.stream().filter(BlockedSlot::isDoctorSide).toList();
  }

  public void validateAvailableSlot(SlotData slotData) {
    maybeVacationScheduleThatCollidesWithDate(slotData.getDate())
        .ifPresent(
            vacationSchedule -> {
              throw new AppointmentCantBeCreatedDueToVacationSchedule(vacationSchedule.getId());
            });
    AppointmentSchedule appointmentSchedule = appointmentScheduleForAppointment(slotData);
    SpecialSchedule specialSchedule;
    if (appointmentSchedule == null) {
      specialSchedule = specialScheduleForAppointment(slotData);
      if (specialSchedule == null) {
        throw new AppointmentCantBeCreatedDueToNoAvailableSlots();
      }
    }
    LocalTime appointmentIntervalTime =
        professionalMedicalCenterRelationship.getAppointmentIntervalTime();
    validateNotConflictingWithAnySlot(slotData, appointmentIntervalTime);
    noDoctorBlockedSlotsColliding(slotData, appointmentIntervalTime);
    if (isMedicalCenterSideAgenda
        || respectsOverlappedAppointmentLimit(slotData, appointmentIntervalTime)) {
      return;
    }
    throw new AppointmentCantBeCreatedDueToNoAvailableSlots();
  }

  private Optional<VacationSchedule> maybeVacationScheduleThatCollidesWithDate(LocalDate date) {
    return vacationSchedules.stream().filter(vacation -> vacation.isInTimeRange(date)).findFirst();
  }

  private void validateNotConflictingWithAnySlot(
      SlotData slotData, LocalTime appointmentIntervalTime) {

    if (isNotConflictingWithAppointments(slotData, appointmentIntervalTime)
        || isNotConflictingWithBlockedSlots(slotData, appointmentIntervalTime)) {
      throw new AppointmentCantBeCreatedDueToNoAvailableSlots();
    }
  }

  private boolean isNotConflictingWithAppointments(
      SlotData slotData, LocalTime appointmentIntervalTime) {
    return appointments.stream()
        .filter(appointment -> appointment.getDate().equals(slotData.getDate()))
        .anyMatch(
            appointment ->
                appointment.isBetweenIntervals(
                        slotData.getSlotStartTime(),
                        appointmentIntervalTime,
                        slotData.getSlotDuration())
                    || ((appointment.isMultiple() || slotData.isMultiple())
                        && appointment.getStartTime().equals(slotData.getSlotStartTime())));
  }

  private boolean isNotConflictingWithBlockedSlots(
      SlotData slotData, LocalTime appointmentIntervalTime) {
    return blockedSlots.stream()
        .filter(blockedSlot -> blockedSlot.getDate().equals(slotData.getDate()))
        .anyMatch(
            blockedSlot ->
                blockedSlot.isBetweenIntervals(
                        slotData.getSlotStartTime(),
                        appointmentIntervalTime,
                        slotData.getSlotDuration())
                    || ((blockedSlot.isMultiple() || slotData.isMultiple())
                        && blockedSlot.getStartTime().equals(slotData.getSlotStartTime())));
  }

  private void noDoctorBlockedSlotsColliding(SlotData slotData, LocalTime appointmentIntervalTime) {
    if (doctorBlockedSlots().stream()
        .anyMatch(
            blockedSlot ->
                blockedSlot.getDate().isEqual(slotData.getDate())
                    && (blockedSlot.startOrEndEquals(
                            slotData.getSlotStartTime(), appointmentIntervalTime)
                        || blockedSlot.startOrEndEquals(
                            slotData.getSlotEndTime(appointmentIntervalTime),
                            appointmentIntervalTime)))) {
      throw new AppointmentCantBeCreatedDueToNoAvailableSlots();
    }
  }

  private AppointmentSchedule appointmentScheduleForAppointment(SlotData slotData) {
    return appointmentSchedules.stream()
        .filter(
            schedule ->
                schedule.isInTimeRange(
                    slotData.getDate(), slotData.getSlotStartTime(), slotData.getSlotDuration()))
        .findFirst()
        .orElse(null);
  }

  private SpecialSchedule specialScheduleForAppointment(SlotData slotData) {
    return specialSchedules.stream()
        .filter(
            schedule ->
                schedule.isInTimeRange(
                    slotData.getDate(), slotData.getSlotStartTime(), slotData.getSlotDuration()))
        .findFirst()
        .orElse(null);
  }

  private boolean respectsOverlappedAppointmentLimit(
      SlotData slotData, LocalTime appointmentIntervalTime) {
    OverlappingAppointmentSum overlappingAppointments =
        countOverlappingAppointments(slotData, appointmentIntervalTime);
    if (overlappingAppointments.highestOverlap() == 0) {
      return true;
    }
    return switch (overlappedAppointmentLimit) {
      case ONE_PER_ONE -> overlappingAppointments.highestOverlap() < 2;
      case ONE_PER_TWO ->
          overlappingAppointments.highestOverlap() < 2
              && hasNoExcessOverlappingAppointmentsInInterval(slotData, 1, appointmentIntervalTime);
      case ONE_PER_THREE ->
          overlappingAppointments.highestOverlap() < 2
              && hasNoExcessOverlappingAppointmentsInInterval(slotData, 2, appointmentIntervalTime);
      case TWO_PER_ONE -> overlappingAppointments.highestOverlap() < 3;
      case NONE -> false;
    };
  }

  private OverlappingAppointmentSum countOverlappingAppointments(
      SlotData slotData, LocalTime appointmentIntervalTime) {
    int overlappingStartingAppointments = 0;
    int overlappingEndingAppointments = 0;
    List<Slot> slots = new ArrayList<>(List.of());
    slots.addAll(appointments);
    slots.addAll(patientBlockedSlots());
    for (Slot slot : slots) {
      if (!slot.getDate().isEqual(slotData.getDate())) {
        continue;
      }
      if (slot.getEndTime(appointmentIntervalTime).equals(slotData.getSlotStartTime())) {
        overlappingEndingAppointments++;
      }
      if (slot.getStartTime().equals(slotData.getSlotEndTime(appointmentIntervalTime))) {
        overlappingStartingAppointments++;
      }
    }
    return new OverlappingAppointmentSum(
        overlappingEndingAppointments, overlappingStartingAppointments);
  }

  private boolean hasNoExcessOverlappingAppointmentsInInterval(
      SlotData slotData, Integer intervalSlots, LocalTime appointmentIntervalTime) {
    long intervalSeconds = appointmentIntervalTime.toSecondOfDay();
    int trueAppointmentSlotInterval = intervalSlots + slotData.getSlotDuration();
    Map<LocalTime, Boolean> slotCounts = new HashMap<>();
    for (int i = -intervalSlots; i <= trueAppointmentSlotInterval; i++) {
      if (i != 0 && i > slotData.getSlotDuration()) { // Skip the startTime itself (i = 0)
        LocalTime slotTime = slotData.getSlotStartTime().plusSeconds(intervalSeconds * i);
        slotCounts.put(slotTime, false); // Initialize count for this slot
      }
    }
    // Check if appointments overlap at each slot time
    for (Appointment appointment : appointments) {
      if (appointment.getDate().isEqual(slotData.getDate())) {
        LocalTime existingStartTime = appointment.getStartTime();
        LocalTime existingEndTime = appointment.getEndTime(appointmentIntervalTime);
        // Only check if the time is one of the slots we're checking and set to true if it exists,
        // if already true it is overlapping
        if (slotCounts.containsKey(existingStartTime)) {
          if (slotCounts.get(existingStartTime)) {
            return false; // excess Overlap found at one of the slots
          }
          slotCounts.put(existingStartTime, true);
        }
        if (slotCounts.containsKey(existingEndTime)) {
          if (slotCounts.get(existingEndTime)) {
            return false; // excess Overlap found at one of the slots
          }
          slotCounts.put(existingEndTime, true);
        }
      }
    }
    return true; // No excess overlap found in any slot
  }
}
