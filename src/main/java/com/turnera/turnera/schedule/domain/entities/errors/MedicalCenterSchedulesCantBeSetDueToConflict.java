package com.turnera.turnera.schedule.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;
import java.time.DayOfWeek;

public class MedicalCenterSchedulesCantBeSetDueToConflict extends ConflictException {

  public MedicalCenterSchedulesCantBeSetDueToConflict(
      Integer medicalCenterId, DayOfWeek dayOfWeek) {
    super(
        ("Cannot set medical center schedules for medical center %s for day %s, because only one schedule can be set for each day of the week. Please remove the conflicting schedules and try again.")
            .formatted(medicalCenterId, dayOfWeek));
  }

  public MedicalCenterSchedulesCantBeSetDueToConflict(
      Integer medicalCenterId, Integer appointmentScheduleId) {
    super(
        ("Cannot set medical center schedules for medical center %s because it conflicts with appointment schedule %s. Please remove the conflicting schedules and try again.")
            .formatted(medicalCenterId, appointmentScheduleId));
  }
}
