package com.turnera.turnera.schedule.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.VacationScheduleInfo;
import com.turnera.turnera.schedule.presentation.entities.VacationScheduleDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "vacation_schedule")
@EqualsAndHashCode
public class VacationSchedule {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "vacation_schedule_id_gen")
  @SequenceGenerator(
      name = "vacation_schedule_id_gen",
      sequenceName = "vacation_schedule_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @NotNull
  @Column(name = "from_date", nullable = false)
  private LocalDate fromDate;

  @NotNull
  @Column(name = "to_date", nullable = false)
  private LocalDate toDate;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "professional_id", insertable = false, updatable = false),
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false)
  })
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  public VacationSchedule() {}

  public VacationSchedule(
      EmployeeUser employeeUser,
      MedicalCenter medicalCenter,
      Professional professional,
      LocalDate fromDate,
      LocalDate toDate) {
    this.medicalCenter = medicalCenter;
    this.professional = professional;
    this.fromDate = fromDate;
    this.toDate = toDate;
    this.createdBy = employeeUser;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public Boolean isInTimeRange(LocalDate date) {
    return (date != null && !date.isBefore(fromDate) && !date.isAfter(toDate));
  }

  public Boolean isInMonthAndYear(Month month, Integer year) {
    return (fromDate.getMonth().equals(month) && fromDate.getYear() == year)
        || (toDate.getMonth().equals(month) && toDate.getYear() == year)
        || (fromDate.isBefore(LocalDate.of(year, month, 1))
            && toDate.isAfter(LocalDate.of(year, month, month.length(year % 4 == 0))));
  }

  public boolean belongsToMedicalCenter(Integer medicalCenterId) {
    return medicalCenter.getId().equals(medicalCenterId);
  }

  public VacationScheduleInfo toVacationScheduleInfo() {
    return VacationScheduleInfo.builder().fromDate(fromDate).toDate(toDate).build();
  }

  public VacationScheduleDTO toDTO() {
    return new VacationScheduleDTO(
        this.getId().longValue(), this.getFromDate().toString(), this.getToDate().toString());
  }
}
