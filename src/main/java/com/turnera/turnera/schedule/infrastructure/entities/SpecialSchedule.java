package com.turnera.turnera.schedule.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.SpecialScheduleInfo;
import com.turnera.turnera.schedule.domain.entities.ScheduleUtils;
import com.turnera.turnera.schedule.presentation.entities.SpecialScheduleDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "special_schedule")
@EqualsAndHashCode
public class SpecialSchedule {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "special_schedule_id_gen")
  @SequenceGenerator(
      name = "special_schedule_id_gen",
      sequenceName = "special_schedule_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @NotNull
  @Column(name = "date", nullable = false)
  private LocalDate date;

  @NotNull
  @Column(name = "start_time", nullable = false)
  private LocalTime startTime;

  @NotNull
  @Column(name = "end_time", nullable = false)
  private LocalTime endTime;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "professional_id", insertable = false, updatable = false),
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false)
  })
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  public SpecialSchedule() {}

  public SpecialSchedule(
      EmployeeUser createdBy,
      MedicalCenter medicalCenter,
      Professional professional,
      LocalDate date,
      LocalTime startTime,
      LocalTime endTime) {
    this.medicalCenter = medicalCenter;
    this.professional = professional;
    this.date = date;
    this.startTime = startTime;
    this.endTime = endTime;
    this.createdBy = createdBy;
  }

  public Boolean isInTimeRange(
      LocalDate date, LocalTime appointmentStartTime, Integer appointmentIntervalAmount) {
    if (!date.equals(this.date)) {
      return false;
    }

    return ScheduleUtils.isInTimeRangeAndAppointmentStartTimeFits(
        this.startTime,
        this.endTime,
        this.professionalMedicalCenterRelationship.getAppointmentIntervalTime(),
        appointmentStartTime,
        appointmentIntervalAmount);
  }

  @PrePersist
  protected void onCreate() {
    createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public Boolean belongsToMedicalCenter(Integer medicalCenterId) {
    return this.medicalCenter.getId().equals(medicalCenterId);
  }

  public DayOfWeek getDayOfWeek() {
    return date.getDayOfWeek();
  }

  public SpecialScheduleInfo toSpecialScheduleInfo() {
    return new SpecialScheduleInfo(date, startTime, endTime);
  }

  public SpecialScheduleDTO toDTO() {
    return new SpecialScheduleDTO(
        this.getId().longValue(),
        this.getDate().toString(),
        this.getStartTime().toString(),
        this.getEndTime().toString());
  }

  public boolean isInMonthAndYear(Month month, int year) {
    return getDate().getMonth().equals(month) && getDate().getYear() == year;
  }
}
