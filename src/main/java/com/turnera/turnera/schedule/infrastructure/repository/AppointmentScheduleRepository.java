package com.turnera.turnera.schedule.infrastructure.repository;

import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class AppointmentScheduleRepository
    implements PanacheRepositoryBase<AppointmentSchedule, Integer> {

  public List<AppointmentSchedule> findAllByMedicalCenterIdAndProfessionalIdAndDays(
      Integer medicalCenterId, Integer professionalId, List<String> weekDays) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND CAST(day AS TEXT) IN ?3",
            medicalCenterId,
            professionalId,
            weekDays)
        .list();
  }

  public List<AppointmentSchedule> findAllByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId) {
    return find("medicalCenter.id = ?1 AND professional.id = ?2", medicalCenterId, professionalId)
        .list();
  }

  public Optional<AppointmentSchedule> findByMedicalCenterIdAndProfessionalIdAndDay(
      Integer medicalCenterId, Integer professionalId, String weekDay) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND CAST(day AS TEXT) = ?3",
            medicalCenterId,
            professionalId,
            weekDay)
        .firstResultOptional();
  }

  public List<AppointmentSchedule> findAllByIdIn(List<Integer> ids) {
    return find("id IN ?1", ids).list();
  }

  public long deleteAllByIdIn(List<Integer> ids) {
    return delete("id IN ?1", ids);
  }
}
