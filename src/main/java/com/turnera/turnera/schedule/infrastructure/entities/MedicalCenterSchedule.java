package com.turnera.turnera.schedule.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleDayInputData;
import com.turnera.turnera.schedule.presentation.entities.ScheduleDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "medical_center_schedule")
@AllArgsConstructor
@NoArgsConstructor
public class MedicalCenterSchedule {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "medical_center_schedule_id_gen")
  @SequenceGenerator(
      name = "medical_center_schedule_id_gen",
      sequenceName = "medical_center_schedule_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @Column(name = "start_time", nullable = false)
  private LocalTime startTime;

  @NotNull
  @Column(name = "end_time", nullable = false)
  private LocalTime endTime;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "updated_by", nullable = false)
  private EmployeeUser updatedBy;

  @NotNull
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @Enumerated(EnumType.STRING)
  @NotNull
  @Column(name = "day", columnDefinition = "week_day", nullable = false)
  private DayOfWeek day;

  public ScheduleDTO toScheduleDto() {
    return new ScheduleDTO(this.id.longValue(), this.day, this.startTime, this.endTime);
  }

  public boolean isDifferentFrom(MedicalCenterScheduleDayInputData modificationData) {
    return !this.startTime.equals(modificationData.getStartTime())
        || !this.endTime.equals(modificationData.getEndTime());
  }

  public void apply(MedicalCenterScheduleDayInputData modificationData, EmployeeUser employeeUser) {
    this.startTime = modificationData.getStartTime();
    this.endTime = modificationData.getEndTime();
    this.updatedAt = BuenosAiresTime.nowAsLocalDateTime();
    this.updatedBy = employeeUser;
  }
}
