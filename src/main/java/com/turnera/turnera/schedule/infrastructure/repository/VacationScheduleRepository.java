package com.turnera.turnera.schedule.infrastructure.repository;

import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;

@ApplicationScoped
public class VacationScheduleRepository
    implements PanacheRepositoryBase<VacationSchedule, Integer> {

  public List<VacationSchedule>
      findAllByMedicalCenterIdAndProfessionalIdAndFromDateGreaterThanEqualOrToDateGreaterThanEqual(
          Integer medicalCenterId, Integer professionalId, LocalDate fromDate, LocalDate toDate) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND (fromDate >= ?3 OR toDate >= ?4)",
            medicalCenterId,
            professionalId,
            fromDate,
            toDate)
        .list();
  }

  public List<VacationSchedule> findByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
      Integer medicalCenterId, Integer professionalId, String dates) {
    return find(
            "SELECT v FROM VacationSchedule v "
                + "WHERE v.medicalCenter.id = ?1 "
                + "AND v.professional.id = ?2 "
                + "AND EXISTS (SELECT 1 FROM unnest(string_to_array(?3, ',')::date[]) AS date "
                + "           WHERE date BETWEEN v.fromDate AND v.toDate)",
            medicalCenterId,
            professionalId,
            dates)
        .list();
  }

  public List<VacationSchedule> findAllByIdIn(List<Integer> ids) {
    return find("id IN ?1", ids).list();
  }

  public long deleteAllByIdIn(List<Integer> ids) {
    return delete("id IN ?1", ids);
  }
}
