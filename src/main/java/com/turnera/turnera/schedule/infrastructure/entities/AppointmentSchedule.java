package com.turnera.turnera.schedule.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.AppointmentScheduleInfo;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import com.turnera.turnera.schedule.domain.entities.ScheduleUtils;
import com.turnera.turnera.schedule.presentation.entities.AppointmentScheduleDTO;
import com.turnera.turnera.schedule.presentation.entities.bodies.AppointmentScheduleDayBody;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "appointment_schedule")
@EqualsAndHashCode
public class AppointmentSchedule {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "appointment_schedule_id_gen")
  @SequenceGenerator(
      name = "appointment_schedule_id_gen",
      sequenceName = "appointment_schedule_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @NotNull
  @Column(name = "starting_at", nullable = false)
  private LocalDate startingAt;

  @Column(name = "ending_at")
  @Getter(AccessLevel.NONE)
  private LocalDate endingAt;

  @NotNull
  @Column(name = "start_time", nullable = false)
  private LocalTime startTime;

  @NotNull
  @Column(name = "end_time", nullable = false)
  private LocalTime endTime;

  @Enumerated(EnumType.STRING)
  @NotNull
  @Column(name = "day", columnDefinition = "week_day", nullable = false)
  private DayOfWeek day;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false),
    @JoinColumn(name = "professional_id", insertable = false, updatable = false)
  })
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  @NotNull
  @Column(name = "weekly_frequency", nullable = false)
  private Integer weeklyFrequency;

  public AppointmentSchedule() {}

  public AppointmentSchedule(
      EmployeeUser employeeUser,
      MedicalCenter medicalCenter,
      Professional professional,
      DayOfWeek weekDay,
      Integer weeklyFrequency,
      LocalTime startTime,
      LocalTime endTime,
      LocalDate startingAt,
      Optional<LocalDate> endingAt) {
    this.medicalCenter = medicalCenter;
    this.professional = professional;
    this.startingAt = startingAt;
    this.endingAt = endingAt.orElse(null);
    this.startTime = startTime;
    this.endTime = endTime;
    this.day = weekDay;
    this.weeklyFrequency = weeklyFrequency;
    this.createdBy = employeeUser;
  }

  public Boolean conflictsWith(AppointmentScheduleDayBody day) {
    if (!this.day.equals(day.getWeekDay())) {
      return false;
    }
    if (startTime.isBefore(day.getStartTime())) {
      return endTime.isAfter(day.getStartTime());
    } else {
      return day.getEndTime().isAfter(startTime);
    }
  }

  public Boolean isInTimeRange(
      LocalDate date, LocalTime appointmentStartTime, Integer appointmentIntervalAmount) {
    DayOfWeek dayOfWeek = date.getDayOfWeek();
    if (!dayOfWeek.equals(this.day)) {
      return false;
    }
    if (this.endingAt != null && date.isAfter(endingAt)) {
      return false;
    }
    if (!isDateInWeeklyFrequency(date)) {
      return false;
    }
    return !date.isBefore(startingAt)
        && ScheduleUtils.isInTimeRangeAndAppointmentStartTimeFits(
            this.startTime,
            this.endTime,
            this.professionalMedicalCenterRelationship.getAppointmentIntervalTime(),
            appointmentStartTime,
            appointmentIntervalAmount);
  }

  private boolean isDateInWeeklyFrequency(LocalDate date) {
    long weeksBetween = java.time.temporal.ChronoUnit.WEEKS.between(startingAt, date);
    return weeksBetween % weeklyFrequency == 0;
  }

  @PrePersist
  protected void onCreate() {
    createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public boolean belongsToMedicalCenter(Integer medicalCenterId) {
    return medicalCenter.getId().equals(medicalCenterId);
  }

  public boolean isDifferentFrom(AppointmentScheduleDayInputData dayBody) {
    return !this.startTime.equals(dayBody.getStartTime())
        || !this.endTime.equals(dayBody.getEndTime())
        || !this.startingAt.equals(dayBody.getStartingAt())
        || !this.endingAt.equals(dayBody.getEndingAt().orElse(null))
        || !this.weeklyFrequency.equals(dayBody.getWeeklyFrequency());
  }

  public void apply(AppointmentScheduleDayInputData modificationData) {
    this.startTime = modificationData.getStartTime();
    this.endTime = modificationData.getEndTime();
    this.weeklyFrequency = modificationData.getWeeklyFrequency();
    this.startingAt = modificationData.getStartingAt();
    this.endingAt = modificationData.getEndingAt().orElse(null);
  }

  public AppointmentScheduleInfo toAppointmentScheduleInfo() {
    return new AppointmentScheduleInfo(
        day.name(), startingAt, endingAt, startTime, endTime, weeklyFrequency);
  }

  public Optional<LocalDate> getEndingAt() {
    return Optional.ofNullable(endingAt);
  }

  public AppointmentScheduleDTO toDTO() {
    return new AppointmentScheduleDTO(
        this.getId().longValue(),
        this.getDay().toString(),
        this.getStartingAt().toString(),
        this.getEndingAt().map(LocalDate::toString).orElse(null),
        this.getStartTime().toString(),
        this.getEndTime().toString(),
        this.getWeeklyFrequency());
  }
}
