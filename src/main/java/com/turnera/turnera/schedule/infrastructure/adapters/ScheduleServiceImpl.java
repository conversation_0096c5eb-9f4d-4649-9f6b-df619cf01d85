package com.turnera.turnera.schedule.infrastructure.adapters;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.*;
import com.turnera.turnera.schedule.domain.entities.errors.AppointmentScheduleNotFoundException;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.infrastructure.repository.AppointmentScheduleRepository;
import com.turnera.turnera.schedule.infrastructure.repository.MedicalCenterScheduleRepository;
import com.turnera.turnera.schedule.infrastructure.repository.SpecialScheduleRepository;
import com.turnera.turnera.schedule.infrastructure.repository.VacationScheduleRepository;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class ScheduleServiceImpl implements ScheduleService {

  private final MedicalCenterScheduleRepository medicalCenterScheduleRepository;
  private final AppointmentScheduleRepository appointmentScheduleRepository;
  private final VacationScheduleRepository vacationScheduleRepository;
  private final SpecialScheduleRepository specialScheduleRepository;
  private final EntityManager entityManager;

  @Override
  public void createVacationSchedule(
      VacationScheduleCreationInput input, VacationScheduleDayBody day) {
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    vacationScheduleRepository.persist(
        new VacationSchedule(
            input.getEmployeeUser(),
            entityManager.getReference(MedicalCenter.class, medicalCenterId),
            entityManager.getReference(Professional.class, professionalId),
            day.getFromDate(),
            day.getToDate()));
  }

  @Override
  public void createSpecialSchedule(
      SpecialScheduleCreationInput input, SpecialScheduleDayBody day) {
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    specialScheduleRepository.persist(
        new SpecialSchedule(
            input.getEmployeeUser(),
            entityManager.getReference(MedicalCenter.class, medicalCenterId),
            entityManager.getReference(Professional.class, professionalId),
            day.getDate(),
            day.getStartTime(),
            day.getEndTime()));
  }

  public List<AppointmentSchedule> findAppointmentSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId) {
    return appointmentScheduleRepository.findAllByMedicalCenterIdAndProfessionalId(
        medicalCenterId, professionalId);
  }

  @Override
  public List<AppointmentSchedule>
      findAppointmentSchedulesByMedicalCenterIdAndProfessionalIdAndWeekDays(
          Integer medicalCenterId, Integer professionalId, List<DayOfWeek> weekDays) {
    return appointmentScheduleRepository.findAllByMedicalCenterIdAndProfessionalIdAndDays(
        medicalCenterId, professionalId, weekDays.stream().map(DayOfWeek::toString).toList());
  }

  @Override
  public AppointmentSchedule findAppointmentScheduleByMedicalCenterIdAndProfessionalIdAndWeekDay(
      Integer medicalCenterId, Integer professionalId, DayOfWeek weekDay) {
    return appointmentScheduleRepository
        .findByMedicalCenterIdAndProfessionalIdAndDay(
            medicalCenterId, professionalId, weekDay.toString())
        .orElseThrow(
            () ->
                new AppointmentScheduleNotFoundException(medicalCenterId, professionalId, weekDay));
  }

  @Override
  public List<VacationSchedule> findVacationSchedulesByMedicalCenterIdAndProfessionalIdAfterDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return vacationScheduleRepository
        .findAllByMedicalCenterIdAndProfessionalIdAndFromDateGreaterThanEqualOrToDateGreaterThanEqual(
            medicalCenterId, professionalId, date, date);
  }

  @Override
  public List<VacationSchedule>
      findVacationSchedulesByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
          Integer medicalCenterId, Integer professionalId, List<LocalDate> dates) {
    String dateString = dates.stream().map(LocalDate::toString).collect(Collectors.joining(","));
    return vacationScheduleRepository.findByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
        medicalCenterId, professionalId, dateString);
  }

  public List<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDateIn(
      Integer medicalCenterId, Integer professionalId, List<LocalDate> dates) {
    return specialScheduleRepository.findByMedicalCenterIdAndProfessionalIdAndDateIn(
        medicalCenterId, professionalId, dates);
  }

  @Override
  public Optional<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return specialScheduleRepository.findByMedicalCenterIdAndProfessionalIdAndDate(
        medicalCenterId, professionalId, date);
  }

  @Override
  public List<AppointmentSchedule> findAllAppointmentSchedulesByIds(List<Integer> integers) {
    return appointmentScheduleRepository.findAllByIdIn(integers);
  }

  @Override
  public void deleteAppointmentSchedules(List<AppointmentSchedule> schedules) {
    List<Integer> ids = schedules.stream().map(AppointmentSchedule::getId).toList();
    appointmentScheduleRepository.deleteAllByIdIn(ids);
  }

  @Override
  public List<SpecialSchedule> findAllSpecialSchedulesByIds(List<Integer> integers) {
    return specialScheduleRepository.findAllByIdIn(integers);
  }

  @Override
  public void deleteSpecialSchedules(List<SpecialSchedule> schedules) {
    List<Integer> ids = schedules.stream().map(SpecialSchedule::getId).toList();
    specialScheduleRepository.deleteAllByIdIn(ids);
  }

  @Override
  public List<VacationSchedule> findALlVacationSchedulesByIds(List<Integer> integers) {
    return vacationScheduleRepository.findAllByIdIn(integers);
  }

  @Override
  public void deleteVacationSchedules(List<VacationSchedule> schedules) {
    List<Integer> ids = schedules.stream().map(VacationSchedule::getId).toList();
    vacationScheduleRepository.deleteAllByIdIn(ids);
  }

  @Override
  public void updateAppointmentSchedule(
      AppointmentSchedule scheduleToModify, AppointmentScheduleDayInputData modificationData) {

    if (scheduleToModify.isDifferentFrom(modificationData)) {
      scheduleToModify.apply(modificationData);
      appointmentScheduleRepository.persist(scheduleToModify);
    }
  }

  @Override
  public List<SpecialSchedule> findSpecialSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId) {
    return specialScheduleRepository
        .findByMedicalCenterIdAndProfessionalIdAndDateIsGreaterThanEqual(
            medicalCenterId, professionalId, BuenosAiresTime.nowAsLocalDate());
  }

  @Override
  public List<VacationSchedule> findVacationSchedulesByMedicalCenterIdAndProfessionalId(
      Integer medicalCenterId, Integer professionalId) {
    return vacationScheduleRepository
        .findAllByMedicalCenterIdAndProfessionalIdAndFromDateGreaterThanEqualOrToDateGreaterThanEqual(
            medicalCenterId,
            professionalId,
            BuenosAiresTime.nowAsLocalDate(),
            BuenosAiresTime.nowAsLocalDate());
  }

  @Override
  public void createAppointmentSchedule(
      AppointmentScheduleCreationInput input, AppointmentScheduleDayInputData day) {
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    AppointmentSchedule schedule =
        day.toAppointmentSchedule(
            input.getEmployeeUser(),
            entityManager.getReference(MedicalCenter.class, medicalCenterId),
            entityManager.getReference(Professional.class, professionalId));
    appointmentScheduleRepository.persist(schedule);
  }

  @Override
  public void createMedicalCenterSchedule(
      MedicalCenterScheduleDayInputData input,
      MedicalCenter medicalCenter,
      EmployeeUser employeeUser) {
    MedicalCenterSchedule schedule = input.toMedicalCenterSchedule(medicalCenter, employeeUser);
    medicalCenterScheduleRepository.persist(schedule);
  }

  @Override
  public void updateMedicalCenterSchedule(
      MedicalCenterSchedule medicalCenterScheduleToModify,
      MedicalCenterScheduleDayInputData modificationData,
      EmployeeUser employeeUser) {
    if (medicalCenterScheduleToModify.isDifferentFrom(modificationData)) {
      medicalCenterScheduleToModify.apply(modificationData, employeeUser);
      medicalCenterScheduleRepository.persist(medicalCenterScheduleToModify);
    }
  }

  @Override
  public void deleteMedicalCenterSchedules(
      List<MedicalCenterSchedule> medicalCenterSchedulesToDelete) {
    List<Integer> ids =
        medicalCenterSchedulesToDelete.stream().map(MedicalCenterSchedule::getId).toList();
    medicalCenterScheduleRepository.deleteAllByIdIn(ids);
  }
}
