package com.turnera.turnera.schedule.infrastructure.repository;

import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class MedicalCenterScheduleRepository
    implements PanacheRepositoryBase<MedicalCenterSchedule, Integer> {

  public long deleteAllByIdIn(List<Integer> ids) {
    return delete("id IN ?1", ids);
  }
}
