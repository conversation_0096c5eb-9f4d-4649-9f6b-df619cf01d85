package com.turnera.turnera.schedule.infrastructure.repository;

import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class SpecialScheduleRepository implements PanacheRepositoryBase<SpecialSchedule, Integer> {

  public List<SpecialSchedule> findByMedicalCenterIdAndProfessionalIdAndDateIn(
      Integer medicalCenterId, Integer professionalId, List<LocalDate> dates) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND date IN ?3",
            medicalCenterId,
            professionalId,
            dates)
        .list();
  }

  public Optional<SpecialSchedule> findByMedicalCenterIdAndProfessionalIdAndDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND date = ?3",
            medicalCenterId,
            professionalId,
            date)
        .firstResultOptional();
  }

  public List<SpecialSchedule> findByMedicalCenterIdAndProfessionalIdAndDateIsGreaterThanEqual(
      Integer medicalCenterId, Integer professionalId, LocalDate localDate) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND date >= ?3",
            medicalCenterId,
            professionalId,
            localDate)
        .list();
  }

  public List<SpecialSchedule> findAllByIdIn(List<Integer> ids) {
    return find("id IN ?1", ids).list();
  }

  public long deleteAllByIdIn(List<Integer> ids) {
    return delete("id IN ?1", ids);
  }
}
