package com.turnera.turnera.schedule.presentation.entities.bodies;

import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import com.turnera.turnera.schedule.presentation.entities.validation.EndTimeAfterStartTime;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
@EndTimeAfterStartTime
public class AppointmentScheduleDayBody {

  private Integer id;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "2023-10-01")
  private LocalDate startingAt;

  @Schema(type = SchemaType.STRING, format = "date", example = "2023-10-01")
  private LocalDate endingAt;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  private LocalTime startTime;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "18:30:00")
  private LocalTime endTime;

  @NotNull private DayOfWeek weekDay;

  @NotNull private Integer weeklyFrequency;

  public AppointmentScheduleDayInputData toDomain() {
    return new AppointmentScheduleDayInputData(
        id,
        startingAt,
        Optional.ofNullable(endingAt),
        startTime,
        endTime,
        weekDay,
        weeklyFrequency);
  }
}
