package com.turnera.turnera.schedule.presentation.entities.bodies;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SpecialScheduleCreationRequest {

  @NotNull private Integer employeeUserId;
  @NotNull private Integer professionalId;
  @NotNull private Integer medicalCenterId;

  @NotNull @Valid private List<SpecialScheduleDayBody> days;
}
