package com.turnera.turnera.schedule.presentation.entities.validation;

import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class DateAndEndTimeAfterNowValidator
    implements ConstraintValidator<DateAndEndTimeAfterNow, Object> {

  @Override
  public boolean isValid(Object object, ConstraintValidatorContext context) {
    switch (object) {
      case SpecialScheduleDayBody day -> {
        return validateDateAndTime(day.getDate(), day.getEndTime());
      }
      case VacationSchedule day -> {
        return validateDate(day.getFromDate());
      }
      default -> {
        return true; // If the object is not one of the supported types, skip validation
      }
    }
  }

  private boolean validateDateAndTime(LocalDate date, LocalTime time) {
    if (date == null || time == null) {
      return true; // Let @NotNull handle null checks
    }
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    LocalDateTime scheduleDateTime = LocalDateTime.of(date, time);
    return !now.isAfter(scheduleDateTime);
  }

  private boolean validateDate(LocalDate date) {
    if (date == null) {
      return true; // Let @NotNull handle null checks
    }
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    return !now.isAfter(date);
  }
}
