package com.turnera.turnera.schedule.presentation.entities.validation;

import com.turnera.turnera.schedule.presentation.entities.bodies.AppointmentScheduleDayBody;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.time.LocalDate;
import java.time.LocalTime;

public class EndTimeAfterStartTimeValidator implements ConstraintValidator<EndTimeAfterStartTime, Object> {

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {
        switch (object) {
            case SpecialScheduleDayBody day -> {
                return validateTimes(day.getStartTime(), day.getEndTime());
            }
            case AppointmentScheduleDayBody day -> {
                return validateTimes(day.getStartTime(), day.getEndTime());
            }
            case VacationScheduleDayBody day -> {
                return validateDates(day.getFromDate(), day.getToDate());
            }
            default -> {
                return true; // If the object is not one of the supported types, skip validation
            }
        }
    }

    private boolean validateTimes(LocalTime startTime, LocalTime endTime) {
        if (startTime == null || endTime == null) {
            return true; // Let @NotNull handle null checks
        }
        return endTime.isAfter(startTime);
    }

    private boolean validateDates(LocalDate fromDate, LocalDate toDate) {
        if (fromDate == null || toDate == null) {
            return true; // Let @NotNull handle null checks
        }
        return !toDate.isBefore(fromDate);
    }
}