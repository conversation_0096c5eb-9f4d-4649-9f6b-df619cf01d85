package com.turnera.turnera.schedule.presentation.entities.bodies;

import static com.turnera.turnera.utils.LocalDateUtils.isBetweenDates;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.validation.DateAndEndTimeAfterNow;
import com.turnera.turnera.schedule.presentation.entities.validation.EndTimeAfterStartTime;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
@EndTimeAfterStartTime
@DateAndEndTimeAfterNow
public class VacationScheduleDayBody {

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  private LocalDate fromDate;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  private LocalDate toDate;

  public Boolean hasConflictWithVacation(VacationSchedule day) {
    return isBetweenDates(getFromDate(), day.getFromDate(), day.getToDate())
        || isBetweenDates(getToDate(), day.getFromDate(), day.getToDate());
  }

  public boolean hasConflictWithSpecialSchedule(SpecialSchedule specialSchedule) {
    return isBetweenDates(specialSchedule.getDate(), getFromDate(), getToDate());
  }

  public boolean hasConflictWithAppointment(Appointment appointment) {
    return isBetweenDates(appointment.getDate(), getFromDate(), getToDate());
  }

  public Stream<LocalDate> getDatesStream() {
    return Stream.of(getFromDate(), getToDate());
  }
}
