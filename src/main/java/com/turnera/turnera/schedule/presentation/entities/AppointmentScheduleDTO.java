package com.turnera.turnera.schedule.presentation.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentScheduleDTO {
  private Long id;

  private String dayOfWeek;

  private String startingAt;

  private String endingAt;

  private String startTime;

  private String endTime;

  private Integer weeklyFrequency;
}
