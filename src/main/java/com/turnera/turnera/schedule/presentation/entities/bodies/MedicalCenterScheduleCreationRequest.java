package com.turnera.turnera.schedule.presentation.entities.bodies;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleCreationInput;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
public class MedicalCenterScheduleCreationRequest {

  @NotNull
  @Schema(description = "ID of the employee user creating the schedule", example = "1")
  private Integer employeeUserId;

  @NotNull
  @Schema(description = "ID of the medical center", example = "1")
  private Integer medicalCenterId;

  @NotEmpty
  @Valid
  @Schema(description = "List of schedule days for the medical center")
  private List<MedicalCenterScheduleDayBody> days;

  public MedicalCenterScheduleCreationInput toDomain(EmployeeUser employeeUser) {
    return new MedicalCenterScheduleCreationInput(
        employeeUser,
        this.medicalCenterId,
        this.days.stream().map(MedicalCenterScheduleDayBody::toDomain).toList());
  }
}
