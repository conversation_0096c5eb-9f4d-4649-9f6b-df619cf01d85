package com.turnera.turnera.schedule.presentation.entities.bodies;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeleteScheduleRequest {
  @NotNull private Integer employeeUserId;
  @NotNull private Integer MedicalCenterId;

  @NotEmpty private List<Integer> scheduleIds;
}
