package com.turnera.turnera.schedule.presentation.entities;

import java.time.DayOfWeek;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ScheduleDTO {
  private Long id;

  private DayOfWeek dayOfWeek;

  private LocalTime startTime;

  private LocalTime endTime;
}
