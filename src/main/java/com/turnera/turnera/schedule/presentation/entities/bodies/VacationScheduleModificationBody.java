package com.turnera.turnera.schedule.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.turnera.turnera.schedule.presentation.entities.validation.DateAndEndTimeAfterNow;
import com.turnera.turnera.schedule.presentation.entities.validation.EndTimeAfterStartTime;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Getter
@Setter
@EndTimeAfterStartTime
@DateAndEndTimeAfterNow
public class VacationScheduleModificationBody {
    @NotNull
    private Integer id;

    @NotNull
    @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate fromDate;

    @NotNull
    @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate toDate;
}