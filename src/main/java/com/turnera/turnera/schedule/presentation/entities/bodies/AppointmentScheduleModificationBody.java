package com.turnera.turnera.schedule.presentation.entities.bodies;

import com.turnera.turnera.schedule.presentation.entities.validation.EndTimeAfterStartTime;
import jakarta.validation.constraints.NotNull;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Getter
@Setter
@EndTimeAfterStartTime
public class AppointmentScheduleModificationBody {
  @NotNull private Integer id;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  private LocalTime startTime;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "18:30:00")
  private LocalTime endTime;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "00:30:00")
  private LocalTime appointmentIntervalTime;
}
