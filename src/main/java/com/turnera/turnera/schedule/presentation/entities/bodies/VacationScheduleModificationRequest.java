package com.turnera.turnera.schedule.presentation.entities.bodies;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class VacationScheduleModificationRequest {
  @NotNull private Integer employeeUserId;

  @NotNull private Integer professionalId;

  @NotNull private Integer medicalCenterId;

  @NotEmpty @Valid private List<VacationScheduleModificationBody> schedules;
}
