package com.turnera.turnera.schedule.presentation.entities.bodies;

import static com.turnera.turnera.utils.LocalDateUtils.isBetweenDates;
import static com.turnera.turnera.utils.LocalTimeUtils.isStrictlyBetweenTimes;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.turnera.turnera.schedule.domain.entities.ScheduleForDay;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.validation.DateAndEndTimeAfterNow;
import com.turnera.turnera.schedule.presentation.entities.validation.EndTimeAfterStartTime;
import jakarta.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
@EndTimeAfterStartTime
@DateAndEndTimeAfterNow
public class SpecialScheduleDayBody implements ScheduleForDay {

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  private LocalDate date;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  private LocalTime startTime;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "18:30:00")
  private LocalTime endTime;

  public DayOfWeek getDayOfWeek() {
    return this.date.getDayOfWeek();
  }

  public Boolean hasConflictWithAppointmentSchedule(AppointmentSchedule day) {
    return getDayOfWeek().equals(day.getDay())
        && (isStrictlyBetweenTimes(getStartTime(), day.getStartTime(), day.getEndTime())
            || isStrictlyBetweenTimes(getEndTime(), day.getStartTime(), day.getEndTime()));
  }

  public Boolean hasConflictWithVacation(VacationSchedule day) {
    return isBetweenDates(getDate(), day.getFromDate(), day.getToDate());
  }

  public boolean hasConflictWithSpecialSchedule(SpecialSchedule specialSchedule) {
    if (!getDate().equals(specialSchedule.getDate())) {
      return false;
    }
    if (getStartTime().isBefore(specialSchedule.getStartTime())) {
      return this.getEndTime().isAfter(specialSchedule.getStartTime());
    } else {
      return specialSchedule.getEndTime().isAfter(this.getStartTime());
    }
  }
}
