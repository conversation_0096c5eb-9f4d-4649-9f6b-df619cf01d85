package com.turnera.turnera.schedule.presentation.entities.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateAndEndTimeAfterNowValidator.class)
public @interface DateAndEndTimeAfterNow {
    String message() default "Date and end time must be after the current time";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}