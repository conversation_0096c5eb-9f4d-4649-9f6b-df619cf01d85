package com.turnera.turnera.schedule.presentation;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.schedule.application.create.SetAppointmentSchedule;
import com.turnera.turnera.schedule.application.create.SetMedicalCenterSchedule;
import com.turnera.turnera.schedule.application.create.SetSpecialSchedule;
import com.turnera.turnera.schedule.application.create.SetVacationSchedule;
import com.turnera.turnera.schedule.application.delete.DeleteSpecialSchedule;
import com.turnera.turnera.schedule.application.delete.DeleteVacationSchedule;
import com.turnera.turnera.schedule.domain.entities.DeleteScheduleInput;
import com.turnera.turnera.schedule.domain.entities.SpecialScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.VacationScheduleCreationInput;
import com.turnera.turnera.schedule.presentation.entities.bodies.*;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;

@Path("/schedule")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ScheduleController {

  private final EmployeeUserValidations employeeUserValidations;
  private final SetAppointmentSchedule setAppointmentSchedule;
  private final SetMedicalCenterSchedule setMedicalCenterSchedule;
  private final SetSpecialSchedule setSpecialSchedule;
  private final SetVacationSchedule setVacationSchedule;
  private final ProfessionalValidations professionalValidations;
  private final DeleteSpecialSchedule deleteSpecialSchedule;
  private final DeleteVacationSchedule deleteVacationSchedule;

  @POST
  @Path("/set-appointment-schedule")
  public Response createScheduleForProfessionalAndMedicalCenter(
      @Valid AppointmentScheduleCreationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    professionalValidations.verifyProfessionalBelongsToMedicalCenter(
        request.getProfessionalId(), request.getMedicalCenterId());
    setAppointmentSchedule.set(request.toDomain(user));
    return Response.ok(
            "Appointment schedule created for professional with ID: "
                + request.getProfessionalId()
                + " and medical center with ID: "
                + request.getMedicalCenterId())
        .build();
  }

  @POST
  @Path("/set-special-schedule")
  public Response createSpecialScheduleForProfessionalAndMedicalCenter(
      @Valid SpecialScheduleCreationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    professionalValidations.verifyProfessionalBelongsToMedicalCenter(
        request.getProfessionalId(), request.getMedicalCenterId());
    SpecialScheduleCreationInput input =
        new SpecialScheduleCreationInput(
            user, request.getMedicalCenterId(), request.getProfessionalId(), request.getDays());
    setSpecialSchedule.set(input);
    return Response.ok(
            "Special schedule created for professional with ID: "
                + request.getProfessionalId()
                + " and medical center with ID: "
                + request.getMedicalCenterId())
        .build();
  }

  @POST
  @Path("/set-vacation-schedule")
  public Response createVacationScheduleForProfessionalAndMedicalCenter(
      @Valid VacationScheduleCreationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    professionalValidations.verifyProfessionalBelongsToMedicalCenter(
        request.getProfessionalId(), request.getMedicalCenterId());
    VacationScheduleCreationInput input =
        new VacationScheduleCreationInput(
            user, request.getMedicalCenterId(), request.getProfessionalId(), request.getDays());
    setVacationSchedule.set(input);
    return Response.ok(
            "Vacation schedule created for professional with ID: "
                + request.getProfessionalId()
                + " and medical center with ID: "
                + request.getMedicalCenterId())
        .build();
  }

  @DELETE
  @Path("/delete-special-schedules")
  public Response deleteSpecialSchedules(@Valid DeleteScheduleRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    DeleteScheduleInput input =
        new DeleteScheduleInput(request.getMedicalCenterId(), user, request.getScheduleIds());
    deleteSpecialSchedule.delete(input);
    return Response.ok("Successfully deleted special schedules").build();
  }

  @DELETE
  @Path("/delete-vacation-schedules")
  public Response deleteVacationSchedules(@Valid DeleteScheduleRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    DeleteScheduleInput input =
        new DeleteScheduleInput(request.getMedicalCenterId(), user, request.getScheduleIds());
    deleteVacationSchedule.delete(input);
    return Response.ok("Successfully deleted vacation schedules").build();
  }

  @PUT
  @Path("/modify-special-schedules")
  public Response modifySpecialSchedules(@Valid SpecialScheduleModificationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    professionalValidations.verifyProfessionalBelongsToMedicalCenter(
        request.getProfessionalId(), request.getMedicalCenterId());

    // Call the service to modify the special schedules
    // modifySpecialSchedule.modify(request.toDomain(user));

    return Response.ok("Successfully modified special schedules").build();
  }

  @PUT
  @Path("/modify-vacation-schedules")
  public Response modifyVacationSchedules(@Valid VacationScheduleModificationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    professionalValidations.verifyProfessionalBelongsToMedicalCenter(
        request.getProfessionalId(), request.getMedicalCenterId());

    // Call the service to modify the vacation schedules
    // modifyVacationSchedule.modify(request.toDomain(user));

    return Response.ok("Successfully modified vacation schedules").build();
  }

  @POST
  @Path("/set-medical-center-schedule")
  public Response createScheduleForMedicalCenter(
      @Valid MedicalCenterScheduleCreationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());

    setMedicalCenterSchedule.set(request.toDomain(user));

    return Response.ok(
            "Medical center schedule created for medical center with ID: "
                + request.getMedicalCenterId())
        .build();
  }
}
