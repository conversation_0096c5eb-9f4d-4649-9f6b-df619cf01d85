package com.turnera.turnera.schedule.presentation.entities.bodies;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleCreationInput;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentScheduleCreationRequest {

  @NotNull private Integer medicalCenterId;
  @NotNull private Integer employeeUserId;
  @NotNull private Integer professionalId;

  @NotNull private List<AppointmentScheduleDayBody> days;

  public AppointmentScheduleCreationInput toDomain(EmployeeUser employeeUser) {
    return new AppointmentScheduleCreationInput(
        employeeUser, medicalCenterId, professionalId, days);
  }
}
