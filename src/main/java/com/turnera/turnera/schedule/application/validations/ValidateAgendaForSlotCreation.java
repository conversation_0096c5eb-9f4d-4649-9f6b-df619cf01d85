package com.turnera.turnera.schedule.application.validations;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForDate;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsByProfessionalAndMedicalCenterAndDate;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.AppointmentAgenda;
import com.turnera.turnera.schedule.domain.entities.AppointmentCreationValidationInput;
import com.turnera.turnera.schedule.domain.entities.SlotData;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ValidateAgendaForSlotCreation {

  private final ScheduleService scheduleService;

  private final FindAppointmentsForMedicalCenterAndProfessionalForDate
      findAppointmentsForMedicalCenterAndProfessionalForDate;

  private final FindBlockedSlotsByProfessionalAndMedicalCenterAndDate
      findBlockedSlotsByProfessionalAndMedicalCenterAndDate;

  @Transactional
  public void validate(AppointmentCreationValidationInput input) {
    ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship =
        input.getProfessionalMedicalCenterRelationship();
    LocalDate date = input.getDate();
    LocalTime startTime = input.getStartTime();
    Integer appointmentSlotDuration = input.getAppointmentSlotDuration();
    Boolean isMedicalCenterSideAgenda = input.getIsMedicalCenterSideAgenda();
    DayOfWeek dayOfWeek = date.getDayOfWeek();
    int professionalId = professionalMedicalCenterRelationship.getProfessional().getId();
    int medicalCenterId = professionalMedicalCenterRelationship.getMedicalCenter().getId();
    log.info(
        APPLICATION,
        "Validating appointment can be created for professional {} in medical center {}  on date {} with startTime {}",
        professionalId,
        medicalCenterId,
        date,
        startTime);
    List<SpecialSchedule> specialSchedules =
        scheduleService
            .findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDate(
                medicalCenterId, professionalId, date)
            .stream()
            .toList();
    List<AppointmentSchedule> appointmentSchedules;
    if (specialSchedules.isEmpty()) {
      appointmentSchedules =
          scheduleService.findAppointmentSchedulesByMedicalCenterIdAndProfessionalIdAndWeekDays(
              medicalCenterId, professionalId, List.of(dayOfWeek));
    } else {
      appointmentSchedules = List.of();
    }
    List<VacationSchedule> vacationSchedules =
        scheduleService.findVacationSchedulesByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
            medicalCenterId, professionalId, List.of(date));
    List<Appointment> appointments =
        findAppointmentsForMedicalCenterAndProfessionalForDate.find(
            medicalCenterId, professionalId, date);
    List<BlockedSlot> blockedSlots =
        findBlockedSlotsByProfessionalAndMedicalCenterAndDate.find(
            professionalId, medicalCenterId, date);
    AppointmentAgenda appointmentAgenda =
        new AppointmentAgenda(
            professionalMedicalCenterRelationship,
            appointments,
            appointmentSchedules,
            specialSchedules,
            vacationSchedules,
            blockedSlots,
            professionalMedicalCenterRelationship.getOverlappedAppointmentLimit(),
            isMedicalCenterSideAgenda);
    appointmentAgenda.validateAvailableSlot(
        new SlotData(date, startTime, appointmentSlotDuration - 1));
    log.info(
        APPLICATION,
        "Appointment can be created for professional {} in medical center {}  on date {} with startTime {}",
        professionalId,
        medicalCenterId,
        date,
        startTime);
  }
}
