package com.turnera.turnera.schedule.application.validations;

import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ScheduleValidations {

  private final ScheduleService scheduleService;

  @Transactional
  public AppointmentSchedule validateScheduleExistsForDateAndProfessionalMedicalCenterRelationship(
      LocalDate date, ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship) {
    log.info(
        "Validating schedule for professional  {} medical center {} with date {} exists",
        professionalMedicalCenterRelationship.getProfessional().getId(),
        professionalMedicalCenterRelationship.getMedicalCenter().getId(),
        date);
    AppointmentSchedule schedule =
        scheduleService.findAppointmentScheduleByMedicalCenterIdAndProfessionalIdAndWeekDay(
            professionalMedicalCenterRelationship.getId().getMedicalCenterId(),
            professionalMedicalCenterRelationship.getId().getProfessionalId(),
            date.getDayOfWeek());
    log.info(
        "Validation successful schedule with id {} exists for professional {} medical center {} with date {}",
        schedule.getId(),
        professionalMedicalCenterRelationship.getProfessional().getId(),
        professionalMedicalCenterRelationship.getMedicalCenter().getId(),
        date);
    return schedule;
  }
}
