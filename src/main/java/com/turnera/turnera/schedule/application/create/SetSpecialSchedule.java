package com.turnera.turnera.schedule.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.SpecialScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.errors.SpecialSchedulesCantBeCreatedDueToConflict;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.SpecialScheduleDayBody;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class SetSpecialSchedule {

  private final ScheduleService scheduleService;

  @Transactional
  public void set(SpecialScheduleCreationInput input) {
    log.info(
        APPLICATION,
        "Creating special schedule for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
    validateNoNewSpecialSchedulesCollideWithEachOther(input);
    MedicalCenter medicalCenter = input.getMedicalCenter();
    medicalCenter.validateSchedulesFit(input.getDays(), input.getProfessionalId());
    List<SpecialScheduleDayBody> specialSchedulesToDefine = input.getDays();
    validateNoNewSpecialSchedulesCollideWithExistingVacationSchedules(input);
    validateNoNewSpecialSchedulesCollideWithExistingSpecialSchedule(input);
    specialSchedulesToDefine.forEach(day -> scheduleService.createSpecialSchedule(input, day));
    log.info(
        APPLICATION,
        "Special schedules created for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
  }

  private void validateNoNewSpecialSchedulesCollideWithEachOther(
      SpecialScheduleCreationInput input) {
    log.info(APPLICATION, "Validating no new special schedules collide with each other");
    List<SpecialScheduleDayBody> daysToSet = input.getDays();

    Map<LocalDate, List<SpecialScheduleDayBody>> daysByLocalDate =
        daysToSet.stream().collect(Collectors.groupingBy(SpecialScheduleDayBody::getDate));

    daysByLocalDate.forEach(
        (weekDay, schedulesForDay) -> {
          if (schedulesForDay.size() > 1) {
            SpecialScheduleDayBody first = schedulesForDay.get(0);
            SpecialScheduleDayBody second = schedulesForDay.get(1);
            log.error(
                APPLICATION,
                "Conflict detected between new schedules - Day: {}, First schedule: {}-{}, Second schedule: {}-{}",
                weekDay,
                first.getStartTime(),
                first.getEndTime(),
                second.getStartTime(),
                second.getEndTime());
            throw new SpecialSchedulesCantBeCreatedDueToConflict(
                input.getMedicalCenterId(), input.getProfessionalId(), first, second);
          }
        });

    log.info(APPLICATION, "No conflicts found between new special schedules");
  }

  private void validateNoNewSpecialSchedulesCollideWithExistingVacationSchedules(
      SpecialScheduleCreationInput input) {
    log.info(
        APPLICATION,
        "Validating no new special schedules collide with existing vacation schedules");
    List<SpecialScheduleDayBody> specialSchedulesToDefine = input.getDays();
    List<LocalDate> dates =
        specialSchedulesToDefine.stream().map(SpecialScheduleDayBody::getDate).distinct().toList();
    List<VacationSchedule> existingVacations =
        scheduleService.findVacationSchedulesByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
            input.getMedicalCenterId(), input.getProfessionalId(), dates);
    existingVacations.forEach(
        vacationSchedule ->
            specialSchedulesToDefine.forEach(
                newSpecialSchedule -> {
                  if (newSpecialSchedule.hasConflictWithVacation(vacationSchedule)) {
                    log.error(
                        APPLICATION,
                        "Conflict detected in requested special schedule day with date: {}, start time: {}, end time: {} with vacation schedule {}",
                        newSpecialSchedule.getDate(),
                        newSpecialSchedule.getStartTime(),
                        newSpecialSchedule.getEndTime(),
                        vacationSchedule.getId());
                    throw new SpecialSchedulesCantBeCreatedDueToConflict(
                        input.getMedicalCenterId(),
                        input.getProfessionalId(),
                        newSpecialSchedule,
                        vacationSchedule);
                  }
                }));
    log.info(
        APPLICATION,
        "No new special schedules collide with existing vacation schedules, validation complete");
  }

  private void validateNoNewSpecialSchedulesCollideWithExistingSpecialSchedule(
      SpecialScheduleCreationInput input) {
    log.info(
        APPLICATION, "Validating no new special schedules collide with existing special schedules");
    List<SpecialScheduleDayBody> specialSchedulesToDefine = input.getDays();
    specialSchedulesToDefine.forEach(
        newSpecialSchedule -> {
          Optional<SpecialSchedule> maybeSpecialSchedule =
              scheduleService.findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDate(
                  input.getMedicalCenterId(),
                  input.getProfessionalId(),
                  newSpecialSchedule.getDate());
          maybeSpecialSchedule.map(
              existingSpecialSchedule -> {
                log.error(
                    APPLICATION,
                    "Conflict detected in requested special schedule day with date: {}, start time: {}, end time: {} with existing special schedule {}",
                    newSpecialSchedule.getDate(),
                    newSpecialSchedule.getStartTime(),
                    newSpecialSchedule.getEndTime(),
                    existingSpecialSchedule.getId());
                throw new SpecialSchedulesCantBeCreatedDueToConflict(
                    input.getMedicalCenterId(),
                    input.getProfessionalId(),
                    newSpecialSchedule,
                    existingSpecialSchedule);
              });
        });
    log.info(
        APPLICATION,
        "No new special schedules collides with existing special schedules, validation complete");
  }
}
