package com.turnera.turnera.schedule.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.application.delete.DeleteMedicalCenterSchedules;
import com.turnera.turnera.schedule.application.modify.ModifyMedicalCenterSchedule;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleDayInputData;
import com.turnera.turnera.schedule.domain.entities.errors.MedicalCenterSchedulesCantBeDeletedDueToConflict;
import com.turnera.turnera.schedule.domain.entities.errors.MedicalCenterSchedulesCantBeSetDueToConflict;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class SetMedicalCenterSchedule {

  private final ModifyMedicalCenterSchedule modifyMedicalCenterSchedule;

  private final CreateMedicalCenterSchedule createMedicalCenterSchedule;

  private final DeleteMedicalCenterSchedules deleteMedicalCenterSchedules;

  @Transactional
  public void set(MedicalCenterScheduleCreationInput input) {
    log.info(
        APPLICATION,
        "Setting medical center schedule for medical center with ID: {}",
        input.getMedicalCenter().getId());
    validateNoNewSchedulesCollideWithEachOther(input);
    Set<MedicalCenterSchedule> existingMedicalCenterSchedules =
        input.getMedicalCenter().getMedicalCenterSchedules();
    Map<Integer, MedicalCenterSchedule> existingSchedulesById =
        existingMedicalCenterSchedules.stream()
            .collect(Collectors.toMap(MedicalCenterSchedule::getId, Function.identity()));
    Map<DayOfWeek, MedicalCenterScheduleDayInputData> newSchedulesByDay =
        input.getDays().stream()
            .collect(Collectors.toMap(MedicalCenterScheduleDayInputData::getWeekDay, day -> day));
    Set<MedicalCenterSchedule> medicalCenterSchedulesToDelete =
        existingMedicalCenterSchedules.stream()
            .filter(
                schedule ->
                    input.getDays().stream()
                        .noneMatch(
                            day ->
                                day.getId().map(id -> id.equals(schedule.getId())).orElse(false)))
            .collect(Collectors.toSet());
    medicalCenterSchedulesToDelete.forEach(this::validateScheduleToDeleteHasNoConflicts);
    newSchedulesByDay.forEach((dayOfWeek, day) -> validateNoConflictingSchedules(input, day));
    newSchedulesByDay.forEach(
        (dayOfWeek, day) ->
            setForDayOfWeek(
                input,
                day,
                day.getId().flatMap(id -> Optional.ofNullable(existingSchedulesById.get(id))),
                dayOfWeek));
    deleteMedicalCenterSchedules.delete(medicalCenterSchedulesToDelete.stream().toList());
    log.info(
        APPLICATION,
        "Medical center schedule set for medical center with ID: {}",
        input.getMedicalCenter().getId());
  }

  public void setForDayOfWeek(
      MedicalCenterScheduleCreationInput input,
      MedicalCenterScheduleDayInputData day,
      Optional<MedicalCenterSchedule> medicalCenterSchedule,
      DayOfWeek dayOfWeek) {
    log.info(APPLICATION, "Setting Medical center schedules for day {}", dayOfWeek.toString());
    medicalCenterSchedule.ifPresentOrElse(
        schedule -> modifyMedicalCenterSchedule.modify(schedule, day, input.getEmployeeUser()),
        () -> createMedicalCenterSchedule.create(input, day));
    log.info(APPLICATION, "Appointment schedules set for day {}", dayOfWeek.toString());
  }

  private void validateNoConflictingSchedules(
      MedicalCenterScheduleCreationInput input, MedicalCenterScheduleDayInputData day) {
    MedicalCenter medicalCenter = input.getMedicalCenter();
    DayOfWeek dayOfWeek = day.getWeekDay();
    log.info(
        APPLICATION,
        "Validating that no conflicting schedules exist for medical center ID: {} on day: {}",
        medicalCenter.getId(),
        dayOfWeek);
    List<AppointmentSchedule> appointmentSchedules =
        medicalCenter.getAppointmentSchedules().stream()
            .filter(schedule -> schedule.getDay().equals(dayOfWeek))
            .toList();
    List<SpecialSchedule> specialSchedules =
        medicalCenter.getSpecialSchedules().stream()
            .filter(schedule -> schedule.getDate().getDayOfWeek().equals(dayOfWeek))
            .toList();
    for (AppointmentSchedule appointmentSchedule : appointmentSchedules) {
      LocalTime startTime = appointmentSchedule.getStartTime();
      LocalTime endTime = appointmentSchedule.getEndTime();
      if (startTime.isBefore(day.getStartTime()) || endTime.isAfter(day.getEndTime())) {
        log.error(
            APPLICATION,
            "Appointment schedule ID: {} with time {}-{} is outside medical center hours {}-{} for day {}",
            appointmentSchedule.getId(),
            startTime,
            endTime,
            day.getStartTime(),
            day.getEndTime(),
            dayOfWeek);
        throw new MedicalCenterSchedulesCantBeSetDueToConflict(
            medicalCenter.getId(), appointmentSchedule.getId());
      }
    }
    for (SpecialSchedule specialSchedule : specialSchedules) {
      LocalTime startTime = specialSchedule.getStartTime();
      LocalTime endTime = specialSchedule.getEndTime();
      if (startTime.isBefore(day.getStartTime()) || endTime.isAfter(day.getEndTime())) {
        log.error(
            APPLICATION,
            "Special schedule ID: {} with time {}-{} is outside medical center hours {}-{} for day {}",
            specialSchedule.getId(),
            startTime,
            endTime,
            day.getStartTime(),
            day.getEndTime(),
            dayOfWeek);
        throw new MedicalCenterSchedulesCantBeSetDueToConflict(
            medicalCenter.getId(), specialSchedule.getId());
      }
    }
  }

  private void validateNoNewSchedulesCollideWithEachOther(
      MedicalCenterScheduleCreationInput input) {
    log.info(APPLICATION, "Validating no new medical center schedules collide with each other");
    List<MedicalCenterScheduleDayInputData> daysToSet = input.getDays();

    Map<DayOfWeek, List<MedicalCenterScheduleDayInputData>> daysByWeekDay =
        daysToSet.stream()
            .collect(Collectors.groupingBy(MedicalCenterScheduleDayInputData::getWeekDay));

    daysByWeekDay.forEach(
        (weekDay, schedulesForDay) -> {
          if (schedulesForDay.size() > 1) {
            log.error(
                APPLICATION,
                "Conflict detected between new medical center schedules, only one medical center schedule per day is allowed - Day: {}",
                weekDay);
            throw new MedicalCenterSchedulesCantBeSetDueToConflict(
                input.getMedicalCenter().getId(), weekDay);
          }
        });
    log.info(APPLICATION, "No conflicts found between new medical center schedules");
  }

  private void validateScheduleToDeleteHasNoConflicts(
      MedicalCenterSchedule medicalCenterSchedulesToDelete) {
    MedicalCenter medicalCenter = medicalCenterSchedulesToDelete.getMedicalCenter();
    DayOfWeek dayOfWeek = medicalCenterSchedulesToDelete.getDay();
    log.info(
        APPLICATION,
        "Validating that schedule {} to delete has no conflicts with existing appointment and special schedules",
        medicalCenterSchedulesToDelete.getId());
    List<AppointmentSchedule> appointmentSchedules =
        medicalCenter.getAppointmentSchedules().stream()
            .filter(schedule -> schedule.getDay().equals(dayOfWeek))
            .toList();
    List<SpecialSchedule> specialSchedules =
        medicalCenter.getSpecialSchedules().stream()
            .filter(schedule -> schedule.getDate().getDayOfWeek().equals(dayOfWeek))
            .toList();
    for (AppointmentSchedule appointmentSchedule : appointmentSchedules) {
      throw new MedicalCenterSchedulesCantBeDeletedDueToConflict(
          medicalCenter.getId(), appointmentSchedule.getId());
    }
    for (SpecialSchedule specialSchedule : specialSchedules) {
      throw new MedicalCenterSchedulesCantBeDeletedDueToConflict(
          medicalCenter.getId(), specialSchedule.getId());
    }
    log.info(
        APPLICATION,
        "No conflicts found for schedule {} to delete with existing appointment and special schedules",
        medicalCenterSchedulesToDelete.getId());
  }
}
