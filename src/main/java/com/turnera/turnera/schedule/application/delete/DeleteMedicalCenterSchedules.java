package com.turnera.turnera.schedule.application.delete;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class DeleteMedicalCenterSchedules {

  private final ScheduleService scheduleService;

  @Transactional
  public void delete(List<MedicalCenterSchedule> medicalCenterSchedulesToDelete) {
    log.info(
        APPLICATION, "Deleting {} medical center schedules", medicalCenterSchedulesToDelete.size());
    scheduleService.deleteMedicalCenterSchedules(medicalCenterSchedulesToDelete);
    log.info(
        APPLICATION,
        "Successfully deleted {} medical center schedules",
        medicalCenterSchedulesToDelete.size());
  }
}
