package com.turnera.turnera.schedule.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.professional.presentation.entities.ScheduleInfo;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindScheduleInfoForProfessionalAndMedicalCenter {

  private final ScheduleService scheduleService;

  @Transactional
  public ScheduleInfo find(Integer ProfessionalId, Integer medicalCenterId) {
    log.info(
        "Finding appointment schedules for professional {} in medical center {}",
        ProfessionalId,
        medicalCenterId);
    List<AppointmentSchedule> appointmentSchedules =
        scheduleService.findAppointmentSchedulesByMedicalCenterIdAndProfessionalId(
            medicalCenterId, ProfessionalId);
    List<SpecialSchedule> specialSchedules =
        scheduleService.findSpecialSchedulesByMedicalCenterIdAndProfessionalId(
            medicalCenterId, ProfessionalId);
    List<VacationSchedule> vacationSchedules =
        scheduleService.findVacationSchedulesByMedicalCenterIdAndProfessionalId(
            medicalCenterId, ProfessionalId);
    log.info(
        APPLICATION,
        "Found {} appointment schedules, {} special schedules and {} vacation schedules for professional {} in medical center {}",
        appointmentSchedules.size(),
        specialSchedules.size(),
        vacationSchedules.size(),
        ProfessionalId,
        medicalCenterId);
    return ScheduleInfo.builder()
        .appointmentSchedules(
            appointmentSchedules.stream()
                .map(AppointmentSchedule::toAppointmentScheduleInfo)
                .toList())
        .specialSchedules(
            specialSchedules.stream().map(SpecialSchedule::toSpecialScheduleInfo).toList())
        .vacationSchedules(
            vacationSchedules.stream().map(VacationSchedule::toVacationScheduleInfo).toList())
        .build();
  }
}
