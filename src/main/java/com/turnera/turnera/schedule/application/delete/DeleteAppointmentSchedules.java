package com.turnera.turnera.schedule.application.delete;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class DeleteAppointmentSchedules {

  private final ScheduleService scheduleService;

  @Transactional
  public void delete(List<AppointmentSchedule> appointmentSchedulesToDelete) {
    log.info(APPLICATION, "Deleting {} appointment schedules", appointmentSchedulesToDelete.size());
    scheduleService.deleteAppointmentSchedules(appointmentSchedulesToDelete);
    log.info(
        APPLICATION,
        "Successfully deleted {} appointment schedules",
        appointmentSchedulesToDelete.size());
  }
}
