package com.turnera.turnera.schedule.application.delete;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForDate;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.ValidateSchedulesBelongToMedicalCenter;
import com.turnera.turnera.schedule.domain.entities.DeleteScheduleInput;
import com.turnera.turnera.schedule.domain.entities.errors.ScheduleCantBeDeletedDueToExistingAppointments;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.DayOfWeek;
import java.util.List;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class DeleteSpecialSchedule {

  private final ScheduleService scheduleService;

  private final FindAppointmentsForMedicalCenterAndProfessionalForDate findAppointmentsForDate;
  private final ValidateSchedulesBelongToMedicalCenter validateSchedulesBelongToMedicalCenter;

  @Transactional
  public void delete(DeleteScheduleInput input) {
    log.info(APPLICATION, "Deleting special schedules with IDs: {}", input.scheduleIds());
    List<SpecialSchedule> schedules =
        scheduleService.findAllSpecialSchedulesByIds(input.scheduleIds());
    Integer medicalCenterId = input.medicalCenterId();
    validateSchedulesBelongToMedicalCenter.validateSpecialSchedules(schedules, medicalCenterId);
    schedules.forEach(
        schedule -> {
          Integer professionalId = schedule.getProfessional().getId();
          DayOfWeek dayOfWeek = schedule.getDayOfWeek();
          List<AppointmentSchedule> appointmentSchedules =
              scheduleService.findAppointmentSchedulesByMedicalCenterIdAndProfessionalIdAndWeekDays(
                  medicalCenterId, professionalId, List.of(dayOfWeek));
          List<Appointment> appointments =
              findAppointmentsForDate.find(medicalCenterId, professionalId, schedule.getDate());
          List<Appointment> conflictingAppointments =
              appointments.stream()
                  .filter(
                      appointment ->
                          appointment.belongsToSpecialSchedule(schedule)
                              && appointmentSchedules.stream()
                                  .noneMatch(appointment::belongsToAppointmentSchedule))
                  .toList();
          if (!conflictingAppointments.isEmpty()) {
            log.error(
                APPLICATION,
                "Cannot delete special schedule {} due to existing appointments {}",
                schedule.getId(),
                conflictingAppointments);
            throw new ScheduleCantBeDeletedDueToExistingAppointments(
                schedule.getId(),
                conflictingAppointments.stream().map(Appointment::getId).toList());
          }
        });
    scheduleService.deleteSpecialSchedules(schedules);
    log.info(APPLICATION, "Successfully deleted {} special schedules", schedules.size());
  }
}
