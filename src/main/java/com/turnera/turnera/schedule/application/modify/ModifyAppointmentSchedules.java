package com.turnera.turnera.schedule.application.modify;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ModifyAppointmentSchedules {

  private final ScheduleService scheduleService;

  @Transactional
  public void modify(
      Map<AppointmentSchedule, AppointmentScheduleDayInputData> appointmentSchedulesToModify) {
    log.info(
        APPLICATION, "Modifying {} appointment schedules", appointmentSchedulesToModify.size());
    appointmentSchedulesToModify.forEach(scheduleService::updateAppointmentSchedule);
    log.info(
        APPLICATION,
        "Successfully modified {} appointment schedules",
        appointmentSchedulesToModify.size());
  }
}
