package com.turnera.turnera.schedule.application.delete;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.ValidateSchedulesBelongToMedicalCenter;
import com.turnera.turnera.schedule.domain.entities.DeleteScheduleInput;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class DeleteVacationSchedule {

  private final ScheduleService scheduleService;
  private final ValidateSchedulesBelongToMedicalCenter validateSchedulesBelongToMedicalCenter;

  @Transactional
  public void delete(DeleteScheduleInput input) {
    log.info(APPLICATION, "Deleting vacation schedules with IDs: {}", input.scheduleIds());
    List<VacationSchedule> schedules =
        scheduleService.findALlVacationSchedulesByIds(input.scheduleIds());
    Integer medicalCenterId = input.medicalCenterId();
    validateSchedulesBelongToMedicalCenter.validateVacationSchedules(schedules, medicalCenterId);
    scheduleService.deleteVacationSchedules(schedules);
    log.info(APPLICATION, "Successfully deleted {} vacation schedules", schedules.size());
  }
}
