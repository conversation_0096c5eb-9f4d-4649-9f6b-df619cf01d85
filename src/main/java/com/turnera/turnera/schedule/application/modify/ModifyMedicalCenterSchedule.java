package com.turnera.turnera.schedule.application.modify;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleDayInputData;
import com.turnera.turnera.schedule.infrastructure.entities.MedicalCenterSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ModifyMedicalCenterSchedule {

  private final ScheduleService scheduleService;

  @Transactional
  public void modify(
      MedicalCenterSchedule medicalCenterScheduleToModify,
      MedicalCenterScheduleDayInputData modificationData,
      EmployeeUser employeeUser) {
    log.info(
        APPLICATION,
        "Modifying medical center schedule with ID: {}",
        medicalCenterScheduleToModify.getId());
    scheduleService.updateMedicalCenterSchedule(
        medicalCenterScheduleToModify, modificationData, employeeUser);
    log.info(
        APPLICATION,
        "Successfully modified medical center schedule with ID: {}",
        medicalCenterScheduleToModify.getId());
  }
}
