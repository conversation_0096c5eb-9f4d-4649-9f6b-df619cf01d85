package com.turnera.turnera.schedule.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.MedicalCenterScheduleDayInputData;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateMedicalCenterSchedule {

  private final ScheduleService scheduleService;

  @Transactional
  public void create(
      MedicalCenterScheduleCreationInput input, MedicalCenterScheduleDayInputData day) {
    log.info(APPLICATION, "Creating medical center schedule for day {}", day.getWeekDay());
    scheduleService.createMedicalCenterSchedule(
        day, input.getMedicalCenter(), input.getEmployeeUser());
    log.info(
        APPLICATION, "Successfully created medical center schedule for day {}", day.getWeekDay());
  }
}
