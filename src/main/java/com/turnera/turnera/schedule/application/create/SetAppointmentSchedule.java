package com.turnera.turnera.schedule.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.schedule.application.delete.DeleteAppointmentSchedules;
import com.turnera.turnera.schedule.application.modify.ModifyAppointmentSchedules;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import com.turnera.turnera.schedule.domain.entities.errors.AppointmentSchedulesCantBeSetDueToConflict;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class SetAppointmentSchedule {

  private final ScheduleService scheduleService;

  private final FindAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek
      findAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek;

  private final CreateAppointmentSchedules createAppointmentSchedules;
  private final ModifyAppointmentSchedules modifyAppointmentSchedules;
  private final DeleteAppointmentSchedules deleteAppointmentSchedules;

  @Transactional
  public void set(AppointmentScheduleCreationInput input) {
    log.info(
        APPLICATION,
        "Setting appointment schedule for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
    MedicalCenter medicalCenter = input.getMedicalCenter();
    medicalCenter.validateSchedulesFit(input.getDays(), input.getProfessionalId());
    validateNoNewAppointmentSchedulesCollideWithEachOther(input);
    List<AppointmentScheduleDayInputData> daysToSet = input.getDays();
    Map<DayOfWeek, List<AppointmentScheduleDayInputData>> daysByWeekDay =
        daysToSet.stream()
            .collect(Collectors.groupingBy(AppointmentScheduleDayInputData::getDayOfWeek));
    daysByWeekDay.forEach(
        (weekDay, schedulesForDay) -> setForDayOfWeek(input, schedulesForDay, weekDay));
    log.info(
        APPLICATION,
        "Appointment schedule set for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
  }

  public void setForDayOfWeek(
      AppointmentScheduleCreationInput input,
      List<AppointmentScheduleDayInputData> days,
      DayOfWeek dayOfWeek) {
    log.info(APPLICATION, "Setting appointment schedules for day {}", dayOfWeek.toString());
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    validateNoConflictingAppointments(input, dayOfWeek);
    List<AppointmentSchedule> existingAppointmentSchedules =
        scheduleService.findAppointmentSchedulesByMedicalCenterIdAndProfessionalIdAndWeekDays(
            medicalCenterId, professionalId, List.of(dayOfWeek));
    List<AppointmentScheduleDayInputData> daysToCreate =
        days.stream().filter(day -> !day.idIsDefined()).toList();
    List<AppointmentScheduleDayInputData> daysToModify =
        days.stream().filter(AppointmentScheduleDayInputData::idIsDefined).toList();
    Map<Integer, AppointmentScheduleDayInputData> daysMap =
        daysToModify.stream()
            .collect(Collectors.toMap(AppointmentScheduleDayInputData::getId, Function.identity()));
    Map<AppointmentSchedule, AppointmentScheduleDayInputData> appointmentSchedulesToModify =
        existingAppointmentSchedules.stream()
            .filter(schedule -> daysMap.containsKey(schedule.getId()))
            .collect(
                Collectors.toMap(Function.identity(), schedule -> daysMap.get(schedule.getId())));
    List<AppointmentSchedule> appointmentSchedulesToDelete =
        existingAppointmentSchedules.stream()
            .filter(
                schedule ->
                    daysToModify.stream().noneMatch(day -> day.getId().equals(schedule.getId())))
            .toList();
    createAppointmentSchedules.create(input, daysToCreate);
    modifyAppointmentSchedules.modify(appointmentSchedulesToModify);
    deleteAppointmentSchedules.delete(appointmentSchedulesToDelete);
    log.info(APPLICATION, "Appointment schedules set for day {}", dayOfWeek.toString());
  }

  private void validateNoConflictingAppointments(
      AppointmentScheduleCreationInput input, DayOfWeek dayOfWeek) {
    log.info(APPLICATION, "Validating no conflicting appointments");
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    List<Appointment> appointments =
        findAppointmentsForMedicalCenterAndProfessionalAndDayOfWeek.find(
            medicalCenterId, professionalId, dayOfWeek);
    List<SpecialSchedule> specialSchedules =
        scheduleService.findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDateIn(
            input.getMedicalCenterId(),
            input.getProfessionalId(),
            appointments.stream().map(Appointment::getDate).distinct().toList());
    List<Appointment> conflictingAppointments =
        appointments.stream()
            .filter(
                appointment ->
                    input.getDays().stream().noneMatch(appointment::belongsToAppointmentSchedule)
                        && specialSchedules.stream()
                            .noneMatch(appointment::belongsToSpecialSchedule))
            .toList();
    if (!conflictingAppointments.isEmpty()) {
      log.error(
          APPLICATION,
          "Cannot set new appointment schedules due to conflicting appointments {}",
          conflictingAppointments.stream().map(Appointment::getId).toList());
      throw new AppointmentSchedulesCantBeSetDueToConflict(
          input.getMedicalCenterId(), input.getProfessionalId(), conflictingAppointments, null);
    }
  }

  private void validateNoNewAppointmentSchedulesCollideWithEachOther(
      AppointmentScheduleCreationInput input) {
    log.info(APPLICATION, "Validating no new appointment schedules collide with each other");
    List<AppointmentScheduleDayInputData> daysToSet = input.getDays();

    Map<DayOfWeek, List<AppointmentScheduleDayInputData>> daysByWeekDay =
        daysToSet.stream()
            .collect(Collectors.groupingBy(AppointmentScheduleDayInputData::getDayOfWeek));

    daysByWeekDay.forEach(
        (weekDay, schedulesForDay) -> {
          for (int i = 0; i < schedulesForDay.size(); i++) {
            AppointmentScheduleDayInputData current = schedulesForDay.get(i);
            for (int j = i + 1; j < schedulesForDay.size(); j++) {
              AppointmentScheduleDayInputData other = schedulesForDay.get(j);
              if (current.conflictsWith(other)) {
                log.error(
                    APPLICATION,
                    "Conflict detected between new schedules - Day: {}, First schedule: {}-{}, Second schedule: {}-{}",
                    weekDay,
                    current.getStartTime(),
                    current.getEndTime(),
                    other.getStartTime(),
                    other.getEndTime());
                throw new AppointmentSchedulesCantBeSetDueToConflict(
                    input.getMedicalCenterId(), input.getProfessionalId(), current, other);
              }
            }
          }
        });

    log.info(APPLICATION, "No conflicts found between new appointment schedules");
  }
}
