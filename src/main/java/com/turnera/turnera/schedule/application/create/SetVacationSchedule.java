package com.turnera.turnera.schedule.application.create;

import com.turnera.turnera.appointment.application.find.FindFutureAppointmentsForMedicalCenterAndProfessional;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.VacationScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.errors.VacationSchedulesCantBeCreatedDueToConflict;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.schedule.presentation.entities.bodies.VacationScheduleDayBody;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class SetVacationSchedule {
  private final FindFutureAppointmentsForMedicalCenterAndProfessional
      findFutureAppointmentsForMedicalCenterAndProfessional;
  // TODO: defined as singular dates, not ranges
  // TODO: fix all this

  private final ScheduleService scheduleService;

  @Transactional
  public void set(VacationScheduleCreationInput input) {
    log.info(
        "Creating vacation schedule for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
    List<VacationScheduleDayBody> vacationSchedulesToDefine = input.getDays();
    validateNoCollisions(input);
    vacationSchedulesToDefine.forEach(day -> scheduleService.createVacationSchedule(input, day));
    log.info(
        "Vacation schedules created for professional with ID: {} and medical center with ID: {}",
        input.getProfessionalId(),
        input.getMedicalCenterId());
  }

  private void validateNoCollisions(VacationScheduleCreationInput input) {
    validateNoNewVacationSchedulesCollideWithExistingVacationSchedules(input);
    validateNoNewVacationSchedulesCollideWithExistingSpecialSchedules(input);
    validateNoNewVacationScheduleCollidesWithExistingAppointments(input);
  }

  private void validateNoNewVacationSchedulesCollideWithExistingVacationSchedules(
      VacationScheduleCreationInput input) {
    log.info("Validating no new vacation schedules collide with existing vacation schedules");
    List<VacationScheduleDayBody> vacationSchedulesToDefine = input.getDays();
    List<LocalDate> dates =
        vacationSchedulesToDefine.stream()
            .flatMap(VacationScheduleDayBody::getDatesStream)
            .toList();
    List<VacationSchedule> existingVacations =
        scheduleService.findVacationSchedulesByMedicalCenterIdAndProfessionalIdAndDatesWithinRange(
            input.getMedicalCenterId(), input.getProfessionalId(), dates);
    existingVacations.forEach(
        existingVacation ->
            vacationSchedulesToDefine.forEach(
                newVacationSchedule -> {
                  if (newVacationSchedule.hasConflictWithVacation(existingVacation)) {
                    log.error("Conflict detected with existing vacation schedule");
                    throw new VacationSchedulesCantBeCreatedDueToConflict(
                        input.getMedicalCenterId(),
                        input.getProfessionalId(),
                        newVacationSchedule,
                        existingVacation);
                  }
                }));
    log.info("No vacation schedules collide with existing vacation schedules, validation complete");
  }

  private void validateNoNewVacationSchedulesCollideWithExistingSpecialSchedules(
      VacationScheduleCreationInput input) {
    log.info("Validating no new vacation schedules collide with existing special schedules");
    List<VacationScheduleDayBody> vacationSchedulesToDefine = input.getDays();
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    // fix this!?
    List<SpecialSchedule> existingSpecialSchedules =
        scheduleService
            .findSpecialSchedulesByMedicalCenterIdAndProfessionalIdAndDate(
                input.getMedicalCenterId(), input.getProfessionalId(), now)
            .stream()
            .toList();
    existingSpecialSchedules.forEach(
        specialSchedule ->
            vacationSchedulesToDefine.forEach(
                day -> {
                  if (day.hasConflictWithSpecialSchedule(specialSchedule)) {
                    log.error("Conflict detected with existing special schedule");
                    throw new VacationSchedulesCantBeCreatedDueToConflict(
                        input.getMedicalCenterId(),
                        input.getProfessionalId(),
                        day,
                        specialSchedule);
                  }
                }));
    log.info("No vacation schedules collide with existing special schedules, validation complete");
  }

  private void validateNoNewVacationScheduleCollidesWithExistingAppointments(
      VacationScheduleCreationInput input) {
    log.info("Validating no vacation schedules collide with existing appointments");
    List<VacationScheduleDayBody> vacationSchedulesToDefine = input.getDays();
    List<Appointment> futureAppointments =
        findFutureAppointmentsForMedicalCenterAndProfessional.find(
            input.getMedicalCenterId(), input.getProfessionalId());
    futureAppointments.forEach(
        appointment ->
            vacationSchedulesToDefine.forEach(
                day -> {
                  if (day.hasConflictWithAppointment(appointment)) {
                    log.error("Conflict detected with existing appointment");
                    throw new VacationSchedulesCantBeCreatedDueToConflict(
                        input.getMedicalCenterId(), input.getProfessionalId(), day, appointment);
                  }
                }));
    log.info("No vacation schedules collide with existing appointments, validation complete");
  }
}
