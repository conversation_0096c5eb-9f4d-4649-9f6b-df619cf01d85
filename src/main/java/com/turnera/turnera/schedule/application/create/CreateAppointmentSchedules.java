package com.turnera.turnera.schedule.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.schedule.domain.ScheduleService;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleCreationInput;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateAppointmentSchedules {

  private final ScheduleService scheduleService;

  @Transactional
  public void create(
      AppointmentScheduleCreationInput input, List<AppointmentScheduleDayInputData> daysToCreate) {
    log.info(APPLICATION, "Creating {} new appointment schedules", daysToCreate.size());
    daysToCreate.forEach(day -> scheduleService.createAppointmentSchedule(input, day));
    log.info(APPLICATION, "Successfully created {} new appointment schedules", daysToCreate.size());
  }
}
