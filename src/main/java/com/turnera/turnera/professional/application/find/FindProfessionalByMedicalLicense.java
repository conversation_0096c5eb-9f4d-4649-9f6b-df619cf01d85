package com.turnera.turnera.professional.application.find;

import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindProfessionalByMedicalLicense {

  private final ProfessionalService professionalService;

  public Professional find(String medicalLicense) {
    return professionalService.findByMedicalLicense(medicalLicense);
  }
}
