package com.turnera.turnera.professional.application.find;

import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindMaybeProfessionalByAuth0Id {

  private final ProfessionalService professionalService;

  public Optional<Professional> find(String auth0Id) {
    log.info("Finding professional by auth0 id {}", auth0Id);
    Optional<Professional> maybeProfessional = professionalService.findMaybeByAuth0Id(auth0Id);
    if (maybeProfessional.isPresent()) {
      log.info("Professional found with auth0 id {}", auth0Id);
    } else {
      log.warn("No professional found with auth0 id {}", auth0Id);
    }
    return maybeProfessional;
  }
}
