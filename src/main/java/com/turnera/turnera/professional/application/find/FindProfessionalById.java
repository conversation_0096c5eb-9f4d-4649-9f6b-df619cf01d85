package com.turnera.turnera.professional.application.find;

import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindProfessionalById {

  private final ProfessionalService professionalService;

  public Professional find(Integer professionalId) {
    return professionalService.findById(professionalId);
  }
}
