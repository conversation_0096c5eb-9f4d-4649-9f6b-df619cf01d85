package com.turnera.turnera.professional.application.validations;

import com.turnera.turnera.professional.application.find.FindProfessionalById;
import com.turnera.turnera.professional.application.find.FindProfessionalByMedicalLicense;
import com.turnera.turnera.professional.application.find.FindProfessionalMedicalCenterRelationshipById;
import com.turnera.turnera.professional.domain.errors.ProfessionalAlreadyExistsException;
import com.turnera.turnera.professional.domain.errors.ProfessionalMedicalCenterRelationshipAlreadyExistsException;
import com.turnera.turnera.professional.domain.errors.ProfessionalMedicalCenterRelationshipNotFoundException;
import com.turnera.turnera.professional.domain.errors.ProfessionalNotFoundException;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ProfessionalValidations {

  private final FindProfessionalById findProfessionalById;
  private final FindProfessionalByMedicalLicense findProfessionalByMedicalLicense;

  private final FindProfessionalMedicalCenterRelationshipById
      findProfessionalMedicalCenterRelationshipById;

  @Transactional
  public Professional verifyProfessionalExists(Integer professionalId) {
    log.info("Verifying if professional with id {} exists", professionalId);
    Professional professional = findProfessionalById.find(professionalId);
    if (professional == null) {
      throw new ProfessionalNotFoundException(professionalId);
    }
    log.info("Professional with id {} exists", professionalId);
    return professional;
  }

  @Transactional
  public ProfessionalMedicalCenterRelationship verifyProfessionalBelongsToMedicalCenter(
      Integer professionalId, Integer medicalCenterId) {
    log.info(
        "Verifying if professional with id {} belongs to medical center with id {}",
        professionalId,
        medicalCenterId);
    ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship =
        findProfessionalMedicalCenterRelationshipById.find(professionalId, medicalCenterId);
    if (professionalMedicalCenterRelationship == null) {
      throw new ProfessionalMedicalCenterRelationshipNotFoundException(
          professionalId, medicalCenterId);
    }
    log.info(
        "Professional with id {} belongs to medical center with id {}",
        professionalId,
        medicalCenterId);
    return professionalMedicalCenterRelationship;
  }

  @Transactional
  public void verifyProfessionalDoesntBelongToMedicalCenter(
      Integer professionalId, Integer medicalCenterId) {
    log.info(
        "Verifying that professional with id {} doesn't belong to medical center with id {}",
        professionalId,
        medicalCenterId);
    ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship =
        findProfessionalMedicalCenterRelationshipById.find(professionalId, medicalCenterId);
    if (professionalMedicalCenterRelationship != null) {
      throw new ProfessionalMedicalCenterRelationshipAlreadyExistsException(
          professionalId, medicalCenterId);
    }
    log.info(
        "Professional with id {} doesn't belong to medical center with id {}",
        professionalId,
        medicalCenterId);
  }

  @Transactional
  public void validateProfessionalDoesntExistWithMedicalLicense(String medicalLicense) {
    log.info("Validating that professional doesn't exist with medical license {}", medicalLicense);
    Professional professional = findProfessionalByMedicalLicense.find(medicalLicense);
    if (professional != null) {
      throw new ProfessionalAlreadyExistsException(professional.getId(), medicalLicense);
    }
    log.info("Professional doesn't exist with medical license {}", medicalLicense);
  }
}
