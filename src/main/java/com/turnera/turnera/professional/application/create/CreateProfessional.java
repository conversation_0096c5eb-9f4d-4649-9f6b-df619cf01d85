package com.turnera.turnera.professional.application.create;

import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalInput;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateProfessional {

  private final ProfessionalService professionalService;
  private final ProfessionalValidations professionalValidations;

  public Professional create(CreateProfessionalInput input) {
    log.info("Creating professional with input {}", input.toJson());
    professionalValidations.validateProfessionalDoesntExistWithMedicalLicense(
        input.getMedicalLicense());
    Professional professional = professionalService.create(input);
    log.info("Professional created with id {}", professional.getId());
    return professional;
  }
}
