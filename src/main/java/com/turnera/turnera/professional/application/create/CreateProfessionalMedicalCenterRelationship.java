package com.turnera.turnera.professional.application.create;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.application.associate.AssociateHealthInsurancesToProfessionalInMedicalCenter;
import com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesInMedicalCenter;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalMedicalCenterRelationshipInput;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreateProfessionalMedicalCenterRelationship {

  private final ProfessionalService professionalService;
  private final ProfessionalValidations professionalValidations;
  private final EmployeeUserValidations employeeUserValidations;
  private final FindHealthInsurancesInMedicalCenter findHealthInsurancesInMedicalCenter;
  private final AssociateHealthInsurancesToProfessionalInMedicalCenter
      associateHealthInsurancesToProfessionalInMedicalCenter;

  @Transactional
  public ProfessionalMedicalCenterRelationship create(
      CreateProfessionalMedicalCenterRelationshipInput input) {
    EmployeeUser createdBy =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            input.getCreatedById(), input.getMedicalCenterId());
    MedicalCenter medicalCenter = createdBy.getMedicalCenter(input.getMedicalCenterId());
    log.info(
        "Creating professional medical center relationship for professional {} in medical center {}, requested by {}",
        input.getProfessionalId(),
        medicalCenter.getId(),
        input.getCreatedById());
    Professional professional =
        professionalValidations.verifyProfessionalExists(input.getProfessionalId());
    professionalValidations.verifyProfessionalDoesntBelongToMedicalCenter(
        input.getProfessionalId(), medicalCenter.getId());
    ProfessionalMedicalCenterRelationship relationship =
        professionalService.createProfessionalMedicalCenterRelationship(
            professional, input, createdBy);
    log.info(
        "Successfully created professional medical center relationship with id: {}",
        relationship.getId());

    List<HealthInsurance> healthInsurances =
        findHealthInsurancesInMedicalCenter.find(medicalCenter.getId()).stream()
            .map(HealthInsuranceMedicalCenterRelationship::getHealthInsurance)
            .toList();
    log.info("Found {} health insurances for medical center", healthInsurances.size());
    log.debug("Associating health insurances with professional in medical center");
    List<HealthInsuranceProfessionalMedicalCenterRelationship> newRelationships =
        associateHealthInsurancesToProfessionalInMedicalCenter.associate(
            healthInsurances, professional, createdBy, medicalCenter);
    log.info("Created {} health insurance relationships for professional", newRelationships.size());
    return relationship;
  }
}
