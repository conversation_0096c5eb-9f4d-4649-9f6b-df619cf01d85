package com.turnera.turnera.professional.application.find;

import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindProfessionalMedicalCenterRelationshipById {

  private final ProfessionalService professionalService;

  public ProfessionalMedicalCenterRelationship find(
      Integer professionalId, Integer medicalCenterId) {
    return professionalService.findRelationshipByProfessionalAndMedicalCenter(
        professionalId, medicalCenterId);
  }
}
