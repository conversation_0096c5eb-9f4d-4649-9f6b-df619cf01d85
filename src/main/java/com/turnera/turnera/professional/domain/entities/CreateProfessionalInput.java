package com.turnera.turnera.professional.domain.entities;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateProfessionalInput {
  private String name;
  private String surname;
  private Optional<String> identificationNumber;
  private String medicalLicense;
  private Integer createdBy;

  public CreateProfessionalInput(
      String name,
      String surname,
      Optional<String> identificationNumber,
      String medicalLicense,
      Integer createdBy) {
    this.name = name;
    this.surname = surname;
    this.identificationNumber = identificationNumber;
    this.medicalLicense = medicalLicense;
    this.createdBy = createdBy;
  }

  public String toJson() {
    try {
      ObjectMapper mapper = new ObjectMapper();
      // Register the Jdk8Module to handle Optional properly
      mapper.registerModule(new Jdk8Module());
      return mapper.writeValueAsString(this);
    } catch (Exception e) {
      throw new RuntimeException("Error converting object to JSON", e);
    }
  }
}
