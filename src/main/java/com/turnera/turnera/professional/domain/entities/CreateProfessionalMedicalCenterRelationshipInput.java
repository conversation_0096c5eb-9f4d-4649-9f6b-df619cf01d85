package com.turnera.turnera.professional.domain.entities;

import com.turnera.turnera.professional.presentation.entities.CreateProfessionalMedicalCenterRelationshipRequest;
import java.time.Duration;
import java.time.LocalTime;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateProfessionalMedicalCenterRelationshipInput {
  private Integer professionalId;
  private Integer medicalCenterId;
  private Optional<String> email;
  private OverlappedAppointmentLimit overlappedAppointmentLimit;
  private Duration maximumAnticipationAppointmentTimeLimit;
  private Duration minimumAnticipationAppointmentTimeLimit;
  private LocalTime appointmentIntervalTime;
  private Integer createdById;

  public CreateProfessionalMedicalCenterRelationshipInput(
      Integer professionalId, CreateProfessionalMedicalCenterRelationshipRequest request) {
    this.medicalCenterId = request.getMedicalCenterId();
    this.professionalId = professionalId;
    this.email = Optional.ofNullable(request.getEmail());
    this.overlappedAppointmentLimit = request.getOverlappedAppointmentLimit();
    this.maximumAnticipationAppointmentTimeLimit =
        Duration.parse(request.getMaximumAnticipationAppointmentTimeLimit());
    this.minimumAnticipationAppointmentTimeLimit =
        Duration.parse(request.getMinimumAnticipationAppointmentTimeLimit());
    this.appointmentIntervalTime = LocalTime.parse(request.getAppointmentIntervalTime());
    this.createdById = request.getCreatedById();
  }
}
