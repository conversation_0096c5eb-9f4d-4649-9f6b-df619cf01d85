package com.turnera.turnera.professional.domain.utils;

import com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesByProvider;
import com.turnera.turnera.healthInsurance.application.find.FindHealthInsurancesForProfessionalInMedicalCenter;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.professional.presentation.entities.*;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class ProfessionalDetailUtils {

  private final FindHealthInsurancesByProvider findHealthInsurancesByProvider;
  private final FindHealthInsurancesForProfessionalInMedicalCenter
      findHealthInsurancesForProfessionalInMedicalCenter;

  public List<HealthInsuranceDTO> getHealthInsuranceInformation(
      Integer professionalId, Integer medicalCenterId) {
    List<HealthInsurance> professionalHealthInsurances =
        findHealthInsurancesForProfessionalInMedicalCenter.find(professionalId, medicalCenterId);
    List<String> providers =
        professionalHealthInsurances.stream().map(HealthInsurance::getName).distinct().toList();
    List<HealthInsuranceDTO> healthInsuranceDTOList =
        providers.stream()
            .map(
                provider ->
                    new HealthInsuranceDTO(
                        provider,
                        findHealthInsurancesByProvider.find(provider).stream()
                            .map(
                                healthInsurance ->
                                    PlanDto.builder()
                                        .name(healthInsurance.getPlan())
                                        .isAccepted(false)
                                        .build())
                            .toList()))
            .toList();
    professionalHealthInsurances.forEach(
        healthInsurance ->
            healthInsuranceDTOList.stream()
                .filter(
                    healthInsuranceDTO ->
                        healthInsuranceDTO.getName().equals(healthInsurance.getName()))
                .findFirst()
                .flatMap(
                    healthInsuranceDTO ->
                        healthInsuranceDTO.getPlans().stream()
                            .filter(plan -> plan.getName().equals(healthInsurance.getPlan()))
                            .findFirst())
                .ifPresent(plan -> plan.setAccepted(true)));
    return healthInsuranceDTOList;
  }
}
