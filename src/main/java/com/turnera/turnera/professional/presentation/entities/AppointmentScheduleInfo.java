package com.turnera.turnera.professional.presentation.entities;

import java.time.LocalDate;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class AppointmentScheduleInfo {
  private String day;

  private LocalDate startingAt;

  private LocalDate endingAt;

  private LocalTime startTime;

  private LocalTime endTime;

  private Integer weeklyFrequency;
}
