package com.turnera.turnera.professional.presentation.entities;

import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationshipId;
import lombok.Data;
import java.util.UUID;

@Data
public class CreateProfessionalMedicalCenterRelationshipResponse {
  private Integer professionalId;
  private Integer medicalCenterId;
  private UUID externalId;

  public CreateProfessionalMedicalCenterRelationshipResponse(
      ProfessionalMedicalCenterRelationshipId relationshipId, UUID externalId) {
    this.professionalId = relationshipId.getProfessionalId();
    this.medicalCenterId = relationshipId.getMedicalCenterId();
    this.externalId = externalId;
  }
}
