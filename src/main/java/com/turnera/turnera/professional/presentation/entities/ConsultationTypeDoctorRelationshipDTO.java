package com.turnera.turnera.professional.presentation.entities;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ConsultationTypeDoctorRelationshipDTO {

  private Integer consultationTypeId;

  private String name;

  private BigDecimal price;

  private boolean availableOnline;

  private boolean acceptsSelfPaidPatient;

  private boolean requiresMedicalOrder;

  private int appointmentIntervalAmount;

  private String instructions;

  private Integer dailyLimit;

  private List<ConsultationHealthInsuranceExtraPropertiesDTO> healthInsuranceExtraProperties;
}
