package com.turnera.turnera.professional.presentation.entities;

import com.turnera.turnera.appointment.presentation.entities.AppointmentDTO;
import com.turnera.turnera.blockedSlot.presentation.entities.BlockedSlotDTO;
import com.turnera.turnera.schedule.presentation.entities.AppointmentScheduleDTO;
import com.turnera.turnera.schedule.presentation.entities.SpecialScheduleDTO;
import com.turnera.turnera.schedule.presentation.entities.VacationScheduleDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfessionalAgendaDTO {

  private String year;

  private String month;

  private List<AppointmentScheduleDTO> appointmentSchedules;

  private List<SpecialScheduleDTO> specialSchedules;

  private List<VacationScheduleDTO> vacationSchedules;

  private List<AppointmentDTO> appointments;

  private List<BlockedSlotDTO> blockedSlots;
}
