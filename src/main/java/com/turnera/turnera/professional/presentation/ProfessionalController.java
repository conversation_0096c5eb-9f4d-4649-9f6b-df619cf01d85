package com.turnera.turnera.professional.presentation;

import com.turnera.turnera.professional.application.create.CreateProfessional;
import com.turnera.turnera.professional.application.create.CreateProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.application.details.ProfessionalDetailService;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalInput;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalMedicalCenterRelationshipInput;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.*;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@Path("/professional")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ProfessionalController {

  private final ProfessionalDetailService professionalDetailService;
  private final CreateProfessional createProfessionalService;
  private final CreateProfessionalMedicalCenterRelationship
      createProfessionalMedicalCenterRelationship;

  @GET
  @Path("/detail")
  public ProfessionalDetailResponse getProfessionalDetail(
      @QueryParam("professionalId") Integer professionalId,
      @QueryParam("medicalCenterId") Integer medicalCenterId) {

    return professionalDetailService.getProfessionalDetail(professionalId, medicalCenterId);
  }

  @POST
  public CreateProfessionalResponse createProfessional(@Valid CreateProfessionalRequest request) {

    CreateProfessionalInput input =
        new CreateProfessionalInput(
            request.getName(),
            request.getSurname(),
            Optional.ofNullable(request.getIdentificationNumber()),
            request.getMedicalLicense(),
            request.getCreatedBy());

    Professional professional = createProfessionalService.create(input);

    return new CreateProfessionalResponse(professional.getId());
  }

  @POST
  @Path("/{professionalId}/medical-center")
  public CreateProfessionalMedicalCenterRelationshipResponse associateProfessionalToMedicalCenter(
      @PathParam("professionalId") Integer professionalId,
      CreateProfessionalMedicalCenterRelationshipRequest request) {
    CreateProfessionalMedicalCenterRelationshipInput input =
        new CreateProfessionalMedicalCenterRelationshipInput(professionalId, request);
    ProfessionalMedicalCenterRelationship newRelationship =
        createProfessionalMedicalCenterRelationship.create(input);
    return new CreateProfessionalMedicalCenterRelationshipResponse(
        newRelationship.getId(), newRelationship.getExternalId());
  }
}
