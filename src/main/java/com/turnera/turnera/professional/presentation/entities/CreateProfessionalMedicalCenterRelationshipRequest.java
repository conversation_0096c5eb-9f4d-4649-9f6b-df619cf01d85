package com.turnera.turnera.professional.presentation.entities;

import com.turnera.turnera.professional.domain.entities.OverlappedAppointmentLimit;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateProfessionalMedicalCenterRelationshipRequest {

  private String email;

  @NotNull(message = "Overlapped appointment limit is required")
  private OverlappedAppointmentLimit overlappedAppointmentLimit;

  @NotNull(message = "Maximum anticipation appointment time limit is required")
  private String maximumAnticipationAppointmentTimeLimit;

  @NotNull(message = "Minimum anticipation appointment time limit is required")
  private String minimumAnticipationAppointmentTimeLimit;

  @NotNull(message = "Appointment interval time is required")
  private String appointmentIntervalTime;

  @NotNull(message = "Medical center ID is required")
  private Integer medicalCenterId;

  @NotNull(message = "Created by ID is required")
  private Integer createdById;
}
