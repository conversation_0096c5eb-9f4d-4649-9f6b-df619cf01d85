package com.turnera.turnera.professional.presentation.entities;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateProfessionalRequest {
    @NotBlank(message = "Name is required")
    private String name;
    
    @NotBlank(message = "Surname is required")
    private String surname;
    
    private String identificationNumber;
    
    @NotBlank(message = "Medical license is required")
    private String medicalLicense;
    
    @NotNull(message = "Created by is required")
    private Integer createdBy;
}
