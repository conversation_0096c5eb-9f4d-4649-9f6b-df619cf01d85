package com.turnera.turnera.professional.infrastructure.adapter;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalInput;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalMedicalCenterRelationshipInput;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationshipId;
import com.turnera.turnera.professional.infrastructure.repository.ProfessionalMedicalCenterRelationshipRepository;
import com.turnera.turnera.professional.infrastructure.repository.ProfessionalRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class ProfessionalServiceImpl implements ProfessionalService {
  private final ProfessionalRepository professionalRepository;

  private final ProfessionalMedicalCenterRelationshipRepository
      professionalMedicalCenterRelationshipRepository;

  @Override
  public Professional findById(Integer professionalId) {
    return professionalRepository.findById(professionalId);
  }

  @Override
  public ProfessionalMedicalCenterRelationship findRelationshipByProfessionalAndMedicalCenter(
      Integer professionalId, Integer medicalCenterId) {
    return professionalMedicalCenterRelationshipRepository.findById(
        new ProfessionalMedicalCenterRelationshipId(professionalId, medicalCenterId));
  }

  @Override
  public Professional create(CreateProfessionalInput input) {
    Professional professional = new Professional(input);
    professionalRepository.persist(professional);
    return professional;
  }

  @Override
  public ProfessionalMedicalCenterRelationship createProfessionalMedicalCenterRelationship(
      Professional professional,
      CreateProfessionalMedicalCenterRelationshipInput input,
      EmployeeUser createdBy) {
    ProfessionalMedicalCenterRelationship relationship =
        new ProfessionalMedicalCenterRelationship(professional, input, createdBy);
    professionalMedicalCenterRelationshipRepository.persist(relationship);
    return relationship;
  }

  @Override
  public Professional findByMedicalLicense(String medicalLicense) {
    return professionalRepository.findByMedicalLicense(medicalLicense);
  }

  @Override
  public Optional<Professional> findMaybeByAuth0Id(String auth0Id) {
    return professionalRepository.findByAuth0Id(auth0Id);
  }
}
