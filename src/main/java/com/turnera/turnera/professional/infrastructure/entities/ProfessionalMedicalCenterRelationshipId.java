package com.turnera.turnera.professional.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.util.Objects;

@Setter
@Getter
@Embeddable
public class ProfessionalMedicalCenterRelationshipId implements java.io.Serializable {
  private static final long serialVersionUID = -6336736711457409510L;

  @NotNull
  @Column(name = "professional_id", nullable = false)
  private Integer professionalId;

  @NotNull
  @Column(name = "medical_center_id", nullable = false)
  private Integer medicalCenterId;

  public ProfessionalMedicalCenterRelationshipId() {}

  public ProfessionalMedicalCenterRelationshipId(Integer professionalId, Integer medicalCenterId) {
    this.professionalId = professionalId;
    this.medicalCenterId = medicalCenterId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    ProfessionalMedicalCenterRelationshipId entity = (ProfessionalMedicalCenterRelationshipId) o;
    return Objects.equals(this.medicalCenterId, entity.medicalCenterId)
        && Objects.equals(this.professionalId, entity.professionalId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(medicalCenterId, professionalId);
  }
}
