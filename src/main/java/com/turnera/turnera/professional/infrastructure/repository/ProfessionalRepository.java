package com.turnera.turnera.professional.infrastructure.repository;

import com.turnera.turnera.professional.infrastructure.entities.Professional;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;

@ApplicationScoped
public class ProfessionalRepository implements PanacheRepositoryBase<Professional, Integer> {

  public Professional findByMedicalLicense(String medicalLicense) {
    return find("medicalLicense", medicalLicense).firstResult();
  }

  public Optional<Professional> findByAuth0Id(String auth0Id) {
    return find("auth0Id", auth0Id).firstResultOptional();
  }
}
