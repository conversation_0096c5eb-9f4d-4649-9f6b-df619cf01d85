package com.turnera.turnera.professional.infrastructure.entities;

import static com.turnera.turnera.utils.StaticConstants.one;
import static com.turnera.turnera.utils.UserType.PROFESSIONAL_USER;

import com.turnera.turnera.professional.domain.entities.CreateProfessionalInput;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.user.presentation.entities.TurneraProfessionalInformationDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Getter
@Setter
@Entity
@Table(name = "professional")
public class Professional extends User {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "professional_id_gen")
  @SequenceGenerator(
      name = "professional_id_gen",
      sequenceName = "professional_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @ColumnDefault("gen_random_uuid()")
  @Column(name = "external_id")
  private UUID externalId;

  @Size(max = 255)
  @Column(name = "identification_number")
  private String identificationNumber;

  @Size(max = 255)
  @NotNull
  @Column(name = "medical_license", nullable = false)
  private String medicalLicense;

  @NotNull
  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @OneToMany(mappedBy = "professional", fetch = FetchType.LAZY)
  private Set<SpecialtyProfessionalMedicalCenterRelationship>
      specialtyProfessionalMedicalCenterRelationships = new LinkedHashSet<>();

  @Size(max = 255)
  @Column(name = "auth0_id")
  private String auth0Id;

  public Professional() {}

  public Professional(CreateProfessionalInput input) {
    setName(input.getName());
    setSurname(input.getSurname());
    this.identificationNumber = input.getIdentificationNumber().orElse(null);
    this.medicalLicense = input.getMedicalLicense();
    this.createdBy = one;
  }

  @PrePersist
  protected void onCreate() {

    createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public UserType getUserType() {
    return PROFESSIONAL_USER;
  }

  public Boolean isMedicalCenterSide() {
    return true; // TODO : ver esto?
  }

  public TurneraProfessionalInformationDTO toResponse() {
    return new TurneraProfessionalInformationDTO(identificationNumber, medicalLicense);
  }
}
