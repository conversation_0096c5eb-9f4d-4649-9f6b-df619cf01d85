package com.turnera.turnera.healthInsurance.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipNotAcceptedError extends BadRequestException {
    public HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipNotAcceptedError(Integer healthInsuranceId, Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        super("Health insurance: " + healthInsuranceId + " not accepted for consultation type: " + consultationTypeId + ", medical center: " + medicalCenterId + " and professional: " + professionalId);
    }
}