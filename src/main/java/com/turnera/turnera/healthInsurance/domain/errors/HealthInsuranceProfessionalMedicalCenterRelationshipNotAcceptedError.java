package com.turnera.turnera.healthInsurance.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class HealthInsuranceProfessionalMedicalCenterRelationshipNotAcceptedError extends BadRequestException {
    public HealthInsuranceProfessionalMedicalCenterRelationshipNotAcceptedError(Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId) {
        super("Health insurance: " + healthInsuranceId + "  not accepted for medical center: " + medicalCenterId + " and professional: " + professionalId);
    }
}