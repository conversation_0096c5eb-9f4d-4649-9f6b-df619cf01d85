package com.turnera.turnera.healthInsurance.domain;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import java.util.List;
import java.util.Optional;

public interface HealthInsuranceService {

  HealthInsurance findById(Integer healthInsuranceId);

  HealthInsuranceProfessionalMedicalCenterRelationship
      findRelationshipByHealthInsuranceIdAndMedicalCenterIdAndProfessionalId(
          Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId)
          throws HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError;

  Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
      findMaybeHealthInsuranceConsultationTypeRelationshipForMedicalCenterAndProfessional(
          Integer healthInsuranceId,
          Integer consultationTypeId,
          Integer medicalCenterId,
          Integer professionalId);

  void associateHealthInsurancesToMedicalCenter(
      List<HealthInsurance> healthInsurances,
      EmployeeUser employeeUser,
      MedicalCenter medicalCenter);

  List<HealthInsuranceProfessionalMedicalCenterRelationship>
      findRelationshipByProfessionalIdAndMedicalCenterId(
          Integer professionalId, Integer medicalCenter);

  List<HealthInsurance> findByProvider(String provider);

  List<HealthInsuranceMedicalCenterRelationship> findHealthInsurancesInMedicalCenter(
      Integer medicalCenter);

  List<HealthInsuranceProfessionalMedicalCenterRelationship>
      associateHealthInsurancesToProfessionalInMedicalCenter(
          List<HealthInsurance> healthInsurances,
          Professional professional,
          EmployeeUser createdBy,
          MedicalCenter medicalCenter);
}
