package com.turnera.turnera.healthInsurance.domain.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError extends BadRequestException {
    public HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError(Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId) {
        super("Health insurance: " + healthInsuranceId + " relationship does not exist for medical center: " + medicalCenterId + " and professional: " + professionalId);
    }
}