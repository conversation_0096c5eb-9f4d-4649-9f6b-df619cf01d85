package com.turnera.turnera.healthInsurance.presentation;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.application.associate.AssociateHealthInsurancesToMedicalCenter;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.constraints.Positive;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;

@Path("/health-insurance")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class HealthInsuranceController {

  private final AssociateHealthInsurancesToMedicalCenter associateHealthInsurancesToMedicalCenter;
  private final EmployeeUserValidations employeeUserValidations;

  @POST
  @Path("/medical-center")
  @Operation(
      summary = "Associate a health insurance with to a medical center",
      description = "This endpoint allows to associate a health insurance with a medical center.")
  public Response associate(
      @QueryParam("employeeUserId") @Positive Integer employeeUserId,
      @QueryParam("medicalCenterId") @Positive Integer medicalCenterId,
      List<Integer> healthInsuranceIds) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            employeeUserId, medicalCenterId);
    associateHealthInsurancesToMedicalCenter.associate(
        healthInsuranceIds, employeeUser, medicalCenterId);
    return Response.ok(
            "Health insurances associated successfully to medical center"
                + medicalCenterId
                + " by user "
                + employeeUser.getId()
                + ".")
        .build();
  }
}
