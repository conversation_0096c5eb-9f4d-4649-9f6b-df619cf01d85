package com.turnera.turnera.healthInsurance.application.validations;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceProfessionalMedicalCenterRelationshipNotAcceptedError;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class HealthInsuranceValidations {
  private final HealthInsuranceService healthInsuranceService;

  @Transactional
  public HealthInsuranceProfessionalMedicalCenterRelationship
      verifyProfessionalMedicalCenterRelationshipAcceptsHealthInsurance(
          Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId) {
    log.info(
        APPLICATION,
        "Verifying if relationship exists for health insurance: {}, medical center: {} and professional: {}",
        healthInsuranceId,
        medicalCenterId,
        professionalId);
    HealthInsuranceProfessionalMedicalCenterRelationship relationship =
        getRelationship(healthInsuranceId, medicalCenterId, professionalId);
    log.info(
        APPLICATION,
        "Relationship exists for health insurance: {}, medical center: {} and professional: {}",
        healthInsuranceId,
        medicalCenterId,
        professionalId);
    return relationship;
  }

  @Transactional
  public List<HealthInsurance> validateHealthInsurancesExist(List<Integer> healthInsuranceIds) {
    log.info(APPLICATION, "Validating {} health insurances exist", healthInsuranceIds.size());
    List<HealthInsurance> healthInsurances =
        healthInsuranceIds.stream().map(healthInsuranceService::findById).toList();
    log.info(APPLICATION, "Validated {} health insurances exist", healthInsurances.size());
    return healthInsurances;
  }

  @Transactional
  public HealthInsurance validateHealthInsuranceExists(Integer healthInsuranceId) {
    log.info(APPLICATION, "Validating health insurance with ID: {}", healthInsuranceId);
    HealthInsurance healthInsurance = healthInsuranceService.findById(healthInsuranceId);
    log.info(APPLICATION, "Health insurance with ID: {} exists", healthInsuranceId);
    return healthInsurance;
  }

  private HealthInsuranceProfessionalMedicalCenterRelationship getRelationship(
      Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId) {
    try {
      return healthInsuranceService
          .findRelationshipByHealthInsuranceIdAndMedicalCenterIdAndProfessionalId(
              healthInsuranceId, medicalCenterId, professionalId);
    } catch (HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError e) {
      throw new HealthInsuranceProfessionalMedicalCenterRelationshipNotAcceptedError(
          healthInsuranceId, medicalCenterId, professionalId);
    }
  }
}
