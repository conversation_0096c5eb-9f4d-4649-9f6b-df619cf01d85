package com.turnera.turnera.healthInsurance.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindHealthInsurancesInMedicalCenter {

  private final HealthInsuranceService healthInsuranceService;

  public List<HealthInsuranceMedicalCenterRelationship> find(Integer medicalCenter) {
    log.info(APPLICATION, "Finding health insurances in medical center {}", medicalCenter);
    List<HealthInsuranceMedicalCenterRelationship> healthInsurances =
        healthInsuranceService.findHealthInsurancesInMedicalCenter(medicalCenter);
    log.info(
        APPLICATION,
        "Found {} health insurances in medical center {}",
        healthInsurances.size(),
        medicalCenter);
    return healthInsurances;
  }
}
