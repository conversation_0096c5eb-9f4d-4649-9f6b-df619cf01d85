package com.turnera.turnera.healthInsurance.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindHealthInsurancesByProvider {

  private final HealthInsuranceService healthInsuranceService;

  public List<HealthInsurance> find(String provider) {
    log.info(APPLICATION, "Finding health insurances from provider ID: {}", provider);
    List<HealthInsurance> healthInsurances = healthInsuranceService.findByProvider(provider);
    log.info("{} health insurances found for provider: {}", healthInsurances.size(), provider);
    return healthInsurances;
  }
}
