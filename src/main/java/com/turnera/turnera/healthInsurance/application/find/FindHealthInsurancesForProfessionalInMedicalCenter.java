package com.turnera.turnera.healthInsurance.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindHealthInsurancesForProfessionalInMedicalCenter {

  private final HealthInsuranceService healthInsuranceService;

  public List<HealthInsurance> find(Integer professionalId, Integer medicalCenter) {
    log.info(
        APPLICATION,
        "Finding professional:{} health insurances in medical center: {}",
        professionalId,
        medicalCenter);
    List<HealthInsurance> healthInsurance =
        healthInsuranceService
            .findRelationshipByProfessionalIdAndMedicalCenterId(professionalId, medicalCenter)
            .stream()
            .map(HealthInsuranceProfessionalMedicalCenterRelationship::getHealthInsurance)
            .toList();
    log.info(
        "Found {} health insurances for professional: {} in medical center: {}",
        healthInsurance.size(),
        professionalId,
        medicalCenter);
    return healthInsurance;
  }
}
