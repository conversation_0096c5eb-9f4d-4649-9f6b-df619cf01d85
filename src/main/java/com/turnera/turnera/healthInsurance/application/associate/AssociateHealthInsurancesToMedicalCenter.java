package com.turnera.turnera.healthInsurance.application.associate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.application.validations.HealthInsuranceValidations;
import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class AssociateHealthInsurancesToMedicalCenter {

  private final HealthInsuranceService healthInsuranceService;
  private final HealthInsuranceValidations healthInsuranceValidations;

  public void associate(
      List<Integer> healthInsuranceIds, EmployeeUser employeeUser, Integer medicalCenterId) {
    MedicalCenter medicalCenter = employeeUser.getMedicalCenter(medicalCenterId);
    log.info(
        APPLICATION,
        "Associating {} health insurances to medical center {}, requested by user",
        healthInsuranceIds.size(),
        medicalCenter.getId());
    List<HealthInsurance> healthInsurances =
        healthInsuranceValidations.validateHealthInsurancesExist(healthInsuranceIds);
    healthInsuranceService.associateHealthInsurancesToMedicalCenter(
        healthInsurances, employeeUser, medicalCenter);
    log.info(
        APPLICATION,
        "Associated {} health insurances to medical center {}",
        healthInsuranceIds.size(),
        medicalCenter.getId());
  }
}
