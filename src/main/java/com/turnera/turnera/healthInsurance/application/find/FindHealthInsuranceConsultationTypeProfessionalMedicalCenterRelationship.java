package com.turnera.turnera.healthInsurance.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindHealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship {

  private final HealthInsuranceService healthInsuranceService;

  public Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship> findMaybe(
      Integer healthInsuranceId,
      Integer consultationTypeId,
      Integer medicalCenterId,
      Integer professionalId) {
    log.info(
        APPLICATION,
        "Finding relationship for health insurance: {}, consultation type: {}, medical center: {} and professional: {}",
        healthInsuranceId,
        consultationTypeId,
        medicalCenterId,
        professionalId);
    Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
        maybeRelationship =
            healthInsuranceService
                .findMaybeHealthInsuranceConsultationTypeRelationshipForMedicalCenterAndProfessional(
                    healthInsuranceId, consultationTypeId, medicalCenterId, professionalId);
    log.info(
        APPLICATION,
        finalLogMessage(
            maybeRelationship,
            healthInsuranceId,
            consultationTypeId,
            medicalCenterId,
            professionalId));
    return maybeRelationship;
  }

  private String finalLogMessage(
      Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
          maybeRelationship,
      Integer healthInsuranceId,
      Integer consultationTypeId,
      Integer medicalCenterId,
      Integer professionalId) {
    if (maybeRelationship.isPresent()) {
      return "Relationship found for health insurance: %d, consultation type: %d, medical center: %d and professional: %d"
          .formatted(healthInsuranceId, consultationTypeId, medicalCenterId, professionalId);
    } else {
      return "Relationship not found for health insurance: %d, consultation type: %d, medical center: %d and professional: %d"
          .formatted(healthInsuranceId, consultationTypeId, medicalCenterId, professionalId);
    }
  }
}
