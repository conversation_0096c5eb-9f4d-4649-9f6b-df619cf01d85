package com.turnera.turnera.healthInsurance.application.associate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class AssociateHealthInsurancesToProfessionalInMedicalCenter {

  private final HealthInsuranceService healthInsuranceService;

  public List<HealthInsuranceProfessionalMedicalCenterRelationship> associate(
      List<HealthInsurance> healthInsurances,
      Professional professional,
      EmployeeUser createdBy,
      MedicalCenter medicalCenter) {
    log.info(
        APPLICATION,
        "Associating {} health insurances to professional {} in medical center {}, requested by {}",
        healthInsurances.size(),
        professional.getId(),
        medicalCenter.getId(),
        createdBy.getId());
    List<HealthInsuranceProfessionalMedicalCenterRelationship> newRelationships =
        healthInsuranceService.associateHealthInsurancesToProfessionalInMedicalCenter(
            healthInsurances, professional, createdBy, medicalCenter);
    log.info(
        APPLICATION,
        "Associated {} health insurances to professional {} in medical center {}, requested by {}",
        newRelationships.size(),
        professional.getId(),
        medicalCenter.getId(),
        createdBy.getId());
    return newRelationships;
  }
}
