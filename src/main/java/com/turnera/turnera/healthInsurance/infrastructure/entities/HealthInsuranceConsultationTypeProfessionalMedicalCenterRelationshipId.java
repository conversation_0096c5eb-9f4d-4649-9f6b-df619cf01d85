package com.turnera.turnera.healthInsurance.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Embeddable
public class HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId implements java.io.Serializable {
    private static final long serialVersionUID = 5992162346729662577L;
    @NotNull
    @Column(name = "health_insurance_id", nullable = false)
    private Integer healthInsuranceId;

    @NotNull
    @Column(name = "consultation_type_id", nullable = false)
    private Integer consultationTypeId;

    @NotNull
    @Column(name = "professional_id", nullable = false)
    private Integer professionalId;

    @NotNull
    @Column(name = "medical_center_id", nullable = false)
    private Integer medicalCenterId;

    public HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId() {
    }

    public HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId(Integer healthInsuranceId, Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        this.healthInsuranceId = healthInsuranceId;
        this.consultationTypeId = consultationTypeId;
        this.medicalCenterId = medicalCenterId;
        this.professionalId = professionalId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId entity = (HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId) o;
        return Objects.equals(this.medicalCenterId, entity.medicalCenterId) &&
                Objects.equals(this.consultationTypeId, entity.consultationTypeId) &&
                Objects.equals(this.healthInsuranceId, entity.healthInsuranceId) &&
                Objects.equals(this.professionalId, entity.professionalId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(medicalCenterId, consultationTypeId, healthInsuranceId, professionalId);
    }

}