package com.turnera.turnera.healthInsurance.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "health_insurance_medical_center_relationship")
public class HealthInsuranceMedicalCenterRelationship {
  @EmbeddedId private HealthInsuranceMedicalCenterRelationshipId id;

  @MapsId("healthInsuranceId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "health_insurance_id", nullable = false)
  private HealthInsurance healthInsurance;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  public HealthInsuranceMedicalCenterRelationship() {}

  public HealthInsuranceMedicalCenterRelationship(
      HealthInsuranceMedicalCenterRelationshipId healthInsuranceMedicalCenterRelationshipId,
      HealthInsurance healthInsurance,
      MedicalCenter medicalCenter,
      EmployeeUser createdBy) {
    this.id = healthInsuranceMedicalCenterRelationshipId;
    this.healthInsurance = healthInsurance;
    this.medicalCenter = medicalCenter;
    this.createdBy = createdBy;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }
}
