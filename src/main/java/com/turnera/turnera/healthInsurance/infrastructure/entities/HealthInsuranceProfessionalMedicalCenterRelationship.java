package com.turnera.turnera.healthInsurance.infrastructure.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "health_insurance_professional_medical_center_relationship")
public class HealthInsuranceProfessionalMedicalCenterRelationship {
  @EmbeddedId private HealthInsuranceProfessionalMedicalCenterRelationshipId id;

  @MapsId("healthInsuranceId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "health_insurance_id", nullable = false)
  private HealthInsurance healthInsurance;

  @MapsId("professionalId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "professional_id", insertable = false, updatable = false),
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false)
  })
  private ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship;

  public HealthInsuranceProfessionalMedicalCenterRelationship() {}

  public HealthInsuranceProfessionalMedicalCenterRelationship(
      HealthInsuranceProfessionalMedicalCenterRelationshipId
          healthInsuranceProfessionalMedicalCenterRelationshipId,
      HealthInsurance healthInsurance,
      Professional professional,
      MedicalCenter medicalCenter,
      EmployeeUser createdBy) {
    this.id = healthInsuranceProfessionalMedicalCenterRelationshipId;
    this.healthInsurance = healthInsurance;
    this.professional = professional;
    this.medicalCenter = medicalCenter;
    this.createdBy = createdBy;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }
}
