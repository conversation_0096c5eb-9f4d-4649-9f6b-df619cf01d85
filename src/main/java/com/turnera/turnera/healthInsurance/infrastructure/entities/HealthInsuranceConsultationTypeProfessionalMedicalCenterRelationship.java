package com.turnera.turnera.healthInsurance.infrastructure.entities;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.presentation.entities.ConsultationHealthInsuranceExtraPropertiesDTO;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "insurance_consultation_professional_medical_center_relationship")
public class HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship {
  @EmbeddedId private HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId id;

  @MapsId("healthInsuranceId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "health_insurance_id", nullable = false)
  private HealthInsurance healthInsurance;

  @MapsId("consultationTypeId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "consultation_type_id", nullable = false)
  private ConsultationType consultationType;

  @MapsId("professionalId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @Column(name = "co_payment_price")
  private BigDecimal coPaymentPrice;

  @NotNull
  @Column(name = "is_excluded", nullable = false)
  private Boolean isExcluded = false;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "updated_by", nullable = false)
  private EmployeeUser updatedBy;

  @NotNull
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(name = "consultation_type_id", insertable = false, updatable = false),
    @JoinColumn(name = "professional_id", insertable = false, updatable = false),
    @JoinColumn(name = "medical_center_id", insertable = false, updatable = false)
  })
  private ConsultationTypeProfessionalMedicalCenterRelationship
      consultationTypeProfessionalMedicalCenterRelationship;

  public ConsultationHealthInsuranceExtraPropertiesDTO toDto() {
    return new ConsultationHealthInsuranceExtraPropertiesDTO(
        healthInsurance.getId(),
        healthInsurance.getName(),
        healthInsurance.getPlan(),
        isExcluded,
        coPaymentPrice);
  }
}
