package com.turnera.turnera.healthInsurance.infrastructure.repository;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class HealthInsuranceRepository implements PanacheRepositoryBase<HealthInsurance, Integer> {

  public List<HealthInsurance> findByName(String provider) {
    return find("name", provider).list();
  }
}
