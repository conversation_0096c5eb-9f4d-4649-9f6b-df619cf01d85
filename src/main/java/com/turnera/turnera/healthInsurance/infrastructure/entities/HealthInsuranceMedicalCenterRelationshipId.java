package com.turnera.turnera.healthInsurance.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.util.Objects;

@Setter
@Getter
@Embeddable
public class HealthInsuranceMedicalCenterRelationshipId implements java.io.Serializable {
    private static final long serialVersionUID = 3044094504209406012L;
    @NotNull
    @Column(name = "health_insurance_id", nullable = false)
    private Integer healthInsuranceId;

    @NotNull
    @Column(name = "medical_center_id", nullable = false)
    private Integer medicalCenterId;

    public HealthInsuranceMedicalCenterRelationshipId() {
    }

    public HealthInsuranceMedicalCenterRelationshipId(Integer healthInsuranceId, Integer medicalCenterId) {
        this.healthInsuranceId = healthInsuranceId;
        this.medicalCenterId = medicalCenterId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        HealthInsuranceMedicalCenterRelationshipId entity = (HealthInsuranceMedicalCenterRelationshipId) o;
        return Objects.equals(this.medicalCenterId, entity.medicalCenterId) &&
                Objects.equals(this.healthInsuranceId, entity.healthInsuranceId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(medicalCenterId, healthInsuranceId);
    }

}