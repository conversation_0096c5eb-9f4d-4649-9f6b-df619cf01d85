package com.turnera.turnera.healthInsurance.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Setter
@Getter
@Entity
@Table(name = "health_insurance")
public class HealthInsurance {
  @Id
  @ColumnDefault("nextval('health_insurance_id_seq'::regclass)")
  @Column(name = "id", nullable = false)
  private Integer id;

  @Size(max = 255)
  @NotNull
  @Column(name = "health_insurance", nullable = false)
  private String name;

  @Size(max = 255)
  @NotNull
  @Column(name = "health_insurance_plan", nullable = false)
  private String plan;

  public String getFullName() {
    return name + " " + " " + plan;
  }
}
