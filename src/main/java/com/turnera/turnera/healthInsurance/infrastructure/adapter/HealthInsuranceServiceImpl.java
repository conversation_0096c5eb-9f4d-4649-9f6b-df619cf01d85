package com.turnera.turnera.healthInsurance.infrastructure.adapter;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceNotFoundException;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError;
import com.turnera.turnera.healthInsurance.infrastructure.entities.*;
import com.turnera.turnera.healthInsurance.infrastructure.repository.*;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class HealthInsuranceServiceImpl implements HealthInsuranceService {

  private final HealthInsuranceProfessionalMedicalCenterRelationshipRepository
      healthInsuranceProfessionalMedicalCenterRelationshipRepository;

  private final HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipRepository
      healthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipRepository;

  private final HealthInsuranceMedicalCenterRelationshipRepository
      healthInsuranceMedicalCenterRelationshipRepository;

  private final HealthInsuranceRepository healthInsuranceRepository;

  @Override
  public HealthInsurance findById(Integer healthInsuranceId) {
    return healthInsuranceRepository
        .findByIdOptional(healthInsuranceId)
        .orElseThrow(() -> new HealthInsuranceNotFoundException(healthInsuranceId));
  }

  @Override
  public HealthInsuranceProfessionalMedicalCenterRelationship
      findRelationshipByHealthInsuranceIdAndMedicalCenterIdAndProfessionalId(
          Integer healthInsuranceId, Integer medicalCenterId, Integer professionalId)
          throws HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError {
    return healthInsuranceProfessionalMedicalCenterRelationshipRepository
        .findByIdOptional(
            new HealthInsuranceProfessionalMedicalCenterRelationshipId(
                healthInsuranceId, medicalCenterId, professionalId))
        .orElseThrow(
            () ->
                new HealthInsuranceProfessionalMedicalCenterRelationshipDoesNotExistError(
                    healthInsuranceId, medicalCenterId, professionalId));
  }

  @Override
  public Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
      findMaybeHealthInsuranceConsultationTypeRelationshipForMedicalCenterAndProfessional(
          Integer healthInsuranceId,
          Integer consultationTypeId,
          Integer medicalCenterId,
          Integer professionalId) {
    return healthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipRepository
        .findByIdOptional(
            new HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId(
                healthInsuranceId, consultationTypeId, medicalCenterId, professionalId));
  }

  @Override
  public List<HealthInsuranceProfessionalMedicalCenterRelationship>
      findRelationshipByProfessionalIdAndMedicalCenterId(
          Integer professionalId, Integer medicalCenter) {
    return healthInsuranceProfessionalMedicalCenterRelationshipRepository
        .findByProfessionalIdAndMedicalCenterId(professionalId, medicalCenter);
  }

  @Override
  public List<HealthInsurance> findByProvider(String provider) {
    return healthInsuranceRepository.findByName(provider);
  }

  @Override
  public List<HealthInsuranceMedicalCenterRelationship> findHealthInsurancesInMedicalCenter(
      Integer medicalCenter) {
    return healthInsuranceMedicalCenterRelationshipRepository.findByMedicalCenterId(medicalCenter);
  }

  @Override
  public void associateHealthInsurancesToMedicalCenter(
      List<HealthInsurance> healthInsurances,
      EmployeeUser employeeUser,
      MedicalCenter medicalCenter) {

    List<HealthInsuranceMedicalCenterRelationship> relationships =
        healthInsurances.stream()
            .map(
                healthInsurance -> {
                  HealthInsuranceMedicalCenterRelationshipId
                      healthInsuranceMedicalCenterRelationshipId =
                          new HealthInsuranceMedicalCenterRelationshipId(
                              healthInsurance.getId(), medicalCenter.getId());
                  return new HealthInsuranceMedicalCenterRelationship(
                      healthInsuranceMedicalCenterRelationshipId,
                      healthInsurance,
                      medicalCenter,
                      employeeUser);
                })
            .toList();
    healthInsuranceMedicalCenterRelationshipRepository.persist(relationships);
  }

  @Override
  public List<HealthInsuranceProfessionalMedicalCenterRelationship>
      associateHealthInsurancesToProfessionalInMedicalCenter(
          List<HealthInsurance> healthInsurances,
          Professional professional,
          EmployeeUser createdBy,
          MedicalCenter medicalCenter) {
    List<HealthInsuranceProfessionalMedicalCenterRelationship> relationships =
        healthInsurances.stream()
            .map(
                healthInsurance -> {
                  HealthInsuranceProfessionalMedicalCenterRelationshipId
                      healthInsuranceProfessionalMedicalCenterRelationshipId =
                          new HealthInsuranceProfessionalMedicalCenterRelationshipId(
                              healthInsurance.getId(), professional.getId(), medicalCenter.getId());
                  return new HealthInsuranceProfessionalMedicalCenterRelationship(
                      healthInsuranceProfessionalMedicalCenterRelationshipId,
                      healthInsurance,
                      professional,
                      medicalCenter,
                      createdBy);
                })
            .toList();
    healthInsuranceProfessionalMedicalCenterRelationshipRepository.persist(relationships);
    return relationships;
  }
}
