package com.turnera.turnera.healthInsurance.infrastructure.repository;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class HealthInsuranceProfessionalMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        HealthInsuranceProfessionalMedicalCenterRelationship,
        HealthInsuranceProfessionalMedicalCenterRelationshipId> {

  public List<HealthInsuranceProfessionalMedicalCenterRelationship>
      findByProfessionalIdAndMedicalCenterId(Integer professionalId, Integer medicalCenter) {
    return find("id.professionalId = ?1 AND id.medicalCenterId = ?2", professionalId, medicalCenter)
        .list();
  }
}
