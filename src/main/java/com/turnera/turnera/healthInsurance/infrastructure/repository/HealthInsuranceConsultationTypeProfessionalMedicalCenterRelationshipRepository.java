package com.turnera.turnera.healthInsurance.infrastructure.repository;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship,
        HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipId> {}
