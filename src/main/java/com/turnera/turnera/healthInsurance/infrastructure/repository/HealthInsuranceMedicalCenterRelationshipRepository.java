package com.turnera.turnera.healthInsurance.infrastructure.repository;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class HealthInsuranceMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        HealthInsuranceMedicalCenterRelationship, HealthInsuranceMedicalCenterRelationshipId> {

  public List<HealthInsuranceMedicalCenterRelationship> findByMedicalCenterId(
      Integer medicalCenterId) {
    return find("id.medicalCenterId", medicalCenterId).list();
  }
}
