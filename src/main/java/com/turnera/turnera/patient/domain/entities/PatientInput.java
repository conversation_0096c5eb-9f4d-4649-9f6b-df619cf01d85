package com.turnera.turnera.patient.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PatientInput {
  private Integer medicalCenterId;
  private EmployeeUser user;
  // Getters and Setters
  private String name;
  private String surname;
  private String identificationNumber;
  private String phone;
  private String email;

  private Optional<HealthInsurance> healthInsurance;

  public PatientInput() {}

  public PatientInput(
      Integer medicalCenterId,
      EmployeeUser user,
      String name,
      String surname,
      String identificationNumber,
      String phone,
      String email,
      Optional<HealthInsurance> healthInsurance) {
    this.medicalCenterId = medicalCenterId;
    this.user = user;
    this.name = name;
    this.surname = surname;
    this.identificationNumber = identificationNumber;
    this.phone = phone;
    this.email = email;
    this.healthInsurance = healthInsurance;
  }

  public Patient toPatient() {
    return new Patient(
        this.name,
        this.surname,
        this.identificationNumber,
        this.phone,
        this.email,
        this.healthInsurance,
        this.user.getId());
  }
}
