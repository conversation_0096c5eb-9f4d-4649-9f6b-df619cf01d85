package com.turnera.turnera.patient.domain;

import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.domain.entities.PatientInputByUser;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.utils.UserType;
import java.util.List;

public interface PatientService {

  Patient createPatientForUser(PatientInputByUser patientInputByUser);

  Patient findById(Integer patientId);

  List<Patient> getPatientsByIdentificationNumberAndPhone(
      String identificationNumber, String phone);

  void migrateOldPatientMedicalCenterRelationships(List<Integer> patientIds, Integer newPatientId);

  Patient findPatientByUserIdAndIdentificationNumber(Integer userId, String identificationNumber);

  void deleteOldPatientProfiles(List<Integer> patientIds);

  Patient findsertPatient(PatientInput input);

  void associateMedicalCenterToPatient(
      Integer medicalCenterId, Integer patientId, Integer userId, UserType creatorType);

  void updateRelationship(PatientMedicalCenterRelationship relationship);

  PatientMedicalCenterRelationship findRelationshipByPatientIdAndMedicalCenterId(
      Integer patientId, Integer medicalCenterId);

  Patient associateHealthInsuranceToPatient(
      Integer patientId, Integer healthInsuranceId, Integer userId, UserType creatorType);
}
