package com.turnera.turnera.patient.domain.entities;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PatientInputByUser {

  private TurneraUser user;
  // Get<PERSON> and Setters
  private String name;
  private String surname;
  private String identificationNumber;
  private String phone;
  private String email;

  private Optional<HealthInsurance> healthInsurance;

  public PatientInputByUser(
      TurneraUser user,
      String name,
      String surname,
      String identificationNumber,
      String phone,
      String email,
      Optional<HealthInsurance> healthInsurance) {
    this.user = user;
    this.name = name;
    this.surname = surname;
    this.identificationNumber = identificationNumber;
    this.phone = phone;
    this.email = email;
    this.healthInsurance = healthInsurance;
  }

  public Patient toPatient() {
    return new Patient(
        this.user,
        this.name,
        this.surname,
        this.identificationNumber,
        this.phone,
        this.email,
        this.healthInsurance,
        this.user.getId());
  }
}
