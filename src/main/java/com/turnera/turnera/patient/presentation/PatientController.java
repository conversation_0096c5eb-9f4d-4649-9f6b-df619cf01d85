package com.turnera.turnera.patient.presentation;

import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.application.validations.HealthInsuranceValidations;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.application.create.CreatePatient;
import com.turnera.turnera.patient.application.create.CreatePatientForUser;
import com.turnera.turnera.patient.application.find.FindPatientByUserIdAndIdentificationNumber;
import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.domain.entities.PatientInputByUser;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.presentation.entities.bodies.PatientCreationRequest;
import com.turnera.turnera.patient.presentation.entities.bodies.PatientCreationRequestForUser;
import com.turnera.turnera.turneraUser.application.validations.UserValidations;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@Path("/patients")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class PatientController {

  private final FindPatientByUserIdAndIdentificationNumber
      findPatientByUserIdAndIdentificationNumber;
  private final CreatePatientForUser createPatientForUser;
  private final CreatePatient createPatient;
  private final UserValidations userValidations;
  private final EmployeeUserValidations employeeUserValidations;
  private final HealthInsuranceValidations healthInsuranceValidations;

  @POST
  @Path("/create-from-user/{userId}")
  public Response createPatientForUser(
      @PathParam("userId") Integer userId, @Valid PatientCreationRequestForUser request) {
    TurneraUser user = userValidations.verifyUserExistsAndIsActive(userId);
    Optional<HealthInsurance> healthInsurance =
        Optional.ofNullable(request.getHealthInsuranceId())
            .map(healthInsuranceValidations::validateHealthInsuranceExists);
    Patient maybePatient =
        findPatientByUserIdAndIdentificationNumber.findPatientByUserIdAndIdentificationNumber(
            userId, request.getIdentificationNumber());
    if (maybePatient != null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity("Patient already exists for user")
          .build();
    }
    PatientInputByUser input =
        new PatientInputByUser(
            user,
            request.getName(),
            request.getSurname(),
            request.getIdentificationNumber(),
            user.getPhone(),
            user.getEmail(),
            healthInsurance);
    Patient patient = createPatientForUser.create(input);
    return Response.ok("Patient created with ID: " + patient.getId()).build();
  }

  @POST
  @Path("/{userId}/{medicalCenterId}")
  public Response createPatientByEmployeeUser(
      @PathParam("userId") Integer userId,
      @PathParam("medicalCenterId") Integer medicalCenterId,
      @Valid PatientCreationRequest request) {
    EmployeeUser user =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(userId, medicalCenterId);
    Optional<HealthInsurance> healthInsurance =
        Optional.ofNullable(request.getHealthInsuranceId())
            .map(healthInsuranceValidations::validateHealthInsuranceExists);
    PatientInput input =
        new PatientInput(
            medicalCenterId,
            user,
            request.getName(),
            request.getSurname(),
            request.getIdentificationNumber(),
            request.getPhone(),
            request.getEmail(),
            healthInsurance);
    Patient patient = createPatient.create(input);
    return Response.ok("Patient created with ID: " + patient.getId()).build();
  }
}
