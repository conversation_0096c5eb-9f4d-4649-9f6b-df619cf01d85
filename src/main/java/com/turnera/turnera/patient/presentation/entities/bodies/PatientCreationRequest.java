package com.turnera.turnera.patient.presentation.entities.bodies;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PatientCreationRequest {
  // Getters and Setters
  @NotBlank(message = "Name is required")
  private String name;

  @NotBlank(message = "Surname is required")
  private String surname;

  @NotBlank(message = "Identification number is required")
  private String identificationNumber;

  @NotBlank(message = "Phone is required")
  @Pattern(
      regexp = "^\\+?[0-9]{10,15}$",
      message = "Phone number must be between 10 and 15 digits and may start with a '+'")
  private String phone;

  @Email(message = "Email must be a valid email address")
  private String email;

  private Integer healthInsuranceId;
}
