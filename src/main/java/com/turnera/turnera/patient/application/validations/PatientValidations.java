package com.turnera.turnera.patient.application.validations;

import com.turnera.turnera.patient.application.find.FindPatientById;
import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.domain.errors.PatientMedicalCenterRelationshipNotFound;
import com.turnera.turnera.patient.domain.errors.PatientNotFoundException;
import com.turnera.turnera.patient.domain.errors.PatientNotOwnedByUserException;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class PatientValidations {

  private final FindPatientById findPatientById;
  private final PatientService patientService;

  @Transactional
  public Patient verifyPatientExists(Integer patientId) throws PatientNotFoundException {
    Patient patient = findPatientById.find(patientId);
    if (patient == null) {
      throw new PatientNotFoundException(patientId);
    }
    return patient;
  }

  @Transactional
  public Patient verifyPatientExistsAndIsOwnedBy(Integer patientId, Integer userId)
      throws PatientNotFoundException, PatientNotOwnedByUserException {
    Patient patient = findPatientById.find(patientId);
    if (patient == null) {
      throw new PatientNotFoundException(patientId);
    }
    if (patient.getUser() == null || !patient.getUser().getId().equals(userId)) {
      throw new PatientNotOwnedByUserException(patientId, userId);
    }
    return patient;
  }

  @Transactional
  public void verifyPatientHasArRelationshipWithMedicalCenter(
      Integer patientId, Integer medicalCenterId) throws PatientNotFoundException {
    PatientMedicalCenterRelationship relationship =
        patientService.findRelationshipByPatientIdAndMedicalCenterId(patientId, medicalCenterId);
    if (relationship == null) {
      throw new PatientMedicalCenterRelationshipNotFound(patientId, medicalCenterId);
    }
  }
}
