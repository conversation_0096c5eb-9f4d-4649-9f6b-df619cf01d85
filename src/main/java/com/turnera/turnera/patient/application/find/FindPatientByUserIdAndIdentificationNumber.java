package com.turnera.turnera.patient.application.find;

import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindPatientByUserIdAndIdentificationNumber {
  private final PatientService patientService;

  public Patient findPatientByUserIdAndIdentificationNumber(
      Integer userId, String identificationNumber) {
    return patientService.findPatientByUserIdAndIdentificationNumber(userId, identificationNumber);
  }
}
