package com.turnera.turnera.patient.application.associate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.utils.UserType;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class AssociateMedicalCenter {

  private final PatientService patientService;

  public void associate(
      Integer medicalCenterId, Integer patientId, Integer userId, UserType creatorType) {
    log.info(
        APPLICATION,
        "Associating Medical Center ID: {} to patient ID: {}",
        medicalCenterId,
        patientId);
    patientService.associateMedicalCenterToPatient(medicalCenterId, patientId, userId, creatorType);
    log.info(
        APPLICATION,
        "Successfully associated Medical Center ID: {} to patient ID: {}",
        medicalCenterId,
        patientId);
  }
}
