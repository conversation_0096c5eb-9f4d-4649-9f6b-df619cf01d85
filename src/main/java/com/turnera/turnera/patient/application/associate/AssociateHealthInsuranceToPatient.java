package com.turnera.turnera.patient.application.associate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.utils.UserType;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class AssociateHealthInsuranceToPatient {

  private final PatientService patientService;

  public void associate(
      Integer healthInsuranceId, Integer patientId, Integer userId, UserType creatorType) {
    log.info(
        APPLICATION,
        "Associating health insurance ID: {} to patient ID: {}",
        healthInsuranceId,
        patientId);
    patientService.associateHealthInsuranceToPatient(
        patientId, healthInsuranceId, userId, creatorType);
    log.info(
        APPLICATION,
        "Successfully associated health insurance ID: {} to patient ID: {}",
        healthInsuranceId,
        patientId);
  }
}
