package com.turnera.turnera.patient.application.update;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.utils.UserType;
import jakarta.enterprise.context.ApplicationScoped;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class UpdateMedicalCenterRelationshipByAppointment {

  private final PatientService patientService;

  public void update(Appointment appointment) {
    Integer medicalCenterId = appointment.getMedicalCenter().getId();
    Integer patientId = appointment.getPatient().getId();
    Integer userId = appointment.getUpdatedBy();
    UserType creatorType = appointment.getCreatorType();
    log.info(
        APPLICATION,
        "Updating medical center relationship for Medical Center ID: {} and Patient ID: {}",
        medicalCenterId,
        patientId);
    PatientMedicalCenterRelationship relationship =
        appointment.getPatientMedicalCenterRelationship();
    if (appointment.isNew()) {
      relationship.setAppointmentCount(relationship.getAppointmentCount() + 1);
      Integer appointmentCount = relationship.getAppointmentCount();
      Integer attendedAppointments = relationship.getCompletedAppointmentCount();
      BigDecimal newAttendancePercentage =
          BigDecimal.valueOf(attendedAppointments + 1).divide(BigDecimal.valueOf(appointmentCount));
      relationship.setAttendancePercentage(newAttendancePercentage);
      relationship.setUpdatedBy(userId);
      relationship.setUpdaterType(creatorType);
    }
    if (appointment.isCancelled()) {
      Integer appointmentCount = relationship.getAppointmentCount();
      Integer attendedAppointments = relationship.getCompletedAppointmentCount();
      BigDecimal newAttendancePercentage =
          BigDecimal.valueOf(attendedAppointments - 1).divide(BigDecimal.valueOf(appointmentCount));
      relationship.setAttendancePercentage(newAttendancePercentage);
    }
    patientService.updateRelationship(relationship);
    log.info(
        APPLICATION,
        "Successfully associated Medical Center ID: {} to patient ID: {}",
        medicalCenterId,
        patientId);
  }
}
