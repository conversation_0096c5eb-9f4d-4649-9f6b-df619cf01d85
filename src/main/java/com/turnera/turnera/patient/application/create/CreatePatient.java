package com.turnera.turnera.patient.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;
import static com.turnera.turnera.utils.UserType.EMPLOYEE_USER;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.application.associate.AssociateMedicalCenter;
import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreatePatient {

  private final PatientService patientService;
  private final AssociateMedicalCenter associateMedicalCenter;

  @Transactional
  public Patient create(PatientInput input) {
    Patient patient = patientService.findsertPatient(input);
    log.info(APPLICATION, "Patient found with ID: {}", patient.getId());
    associateMedicalCenter.associate(
        input.getMedicalCenterId(), patient.getId(), input.getUser().getId(), EMPLOYEE_USER);
    log.info(
        APPLICATION,
        "Patient with Id: {} associated with Health Insurance Id: {} and Medical Center Id: {}",
        patient.getId(),
        input.getHealthInsurance().map(HealthInsurance::getId).orElse(null),
        input.getMedicalCenterId());
    return patient;
  }
}
