package com.turnera.turnera.patient.application.create;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;
import static com.turnera.turnera.utils.UserType.TURNERA_USER;

import com.turnera.turnera.appointment.application.migrate.MigrateAppointments;
import com.turnera.turnera.patient.application.associate.AssociateHealthInsuranceToPatient;
import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.domain.entities.PatientInputByUser;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CreatePatientForUser {

  private final PatientService patientService;
  private final MigrateAppointments migrateAppointments;
  private final AssociateHealthInsuranceToPatient associateHealthInsuranceToPatient;

  @Transactional
  public Patient create(PatientInputByUser patientInputByUser) {
    Patient newPatient = patientService.createPatientForUser(patientInputByUser);
    log.info(APPLICATION, "Patient created with ID: {}", newPatient.getId());
    migrateOldPatientData(newPatient, patientInputByUser);
    return newPatient;
  }

  void migrateOldPatientData(Patient newPatient, PatientInputByUser patientInputByUser) {
    log.info(APPLICATION, "New patient was created proceeding to migrate old patients info");
    List<Patient> oldPatients =
        patientService
            .getPatientsByIdentificationNumberAndPhone(
                patientInputByUser.getIdentificationNumber(), patientInputByUser.getPhone())
            .stream()
            .filter(patient -> !patient.getId().equals(newPatient.getId()))
            .toList();
    log.info(APPLICATION, "Found {} old patients to migrate", oldPatients.size());
    List<Integer> oldPatientIds = oldPatients.stream().map(Patient::getId).toList();
    Integer newPatientId = newPatient.getId();
    migrateAppointments.migrateOldPatientAppointmentsToNewPatientId(oldPatientIds, newPatientId);
    log.info(
        APPLICATION,
        "Migrating old patient's medical center relationships to new patient ID: {}",
        newPatientId);
    log.info(
        APPLICATION,
        "Successfully migrated old patient's medical center relationships to new patient ID: {}",
        newPatientId);
    patientService.migrateOldPatientMedicalCenterRelationships(oldPatientIds, newPatientId);
    log.info(APPLICATION, "Deleting old patient profiles");
    patientService.deleteOldPatientProfiles(oldPatientIds);
    log.info(APPLICATION, "Successfully deleted old patient profiles");
    log.info(
        APPLICATION,
        "Successfully migrated old patient's information to new patient ID: {}",
        newPatientId);
    patientInputByUser
        .getHealthInsurance()
        .ifPresent(
            healthInsurance ->
                associateHealthInsuranceToPatient.associate(
                    healthInsurance.getId(),
                    newPatientId,
                    patientInputByUser.getUser().getId(),
                    TURNERA_USER));
  }
}
