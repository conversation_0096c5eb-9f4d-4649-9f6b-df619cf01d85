package com.turnera.turnera.patient.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

@Setter
@Getter
@Embeddable
public class PatientMedicalCenterRelationshipId implements java.io.Serializable {
    private static final long serialVersionUID = -3878173123106551007L;
    @NotNull
    @Column(name = "patient_id", nullable = false)
    private Integer patientId;

    @NotNull
    @Column(name = "medical_center_id", nullable = false)
    private Integer medicalCenterId;

    public PatientMedicalCenterRelationshipId() {
    }

    public PatientMedicalCenterRelationshipId(Integer patientId, Integer medicalCenterId) {
        this.patientId = patientId;
        this.medicalCenterId = medicalCenterId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        PatientMedicalCenterRelationshipId entity = (PatientMedicalCenterRelationshipId) o;
        return Objects.equals(this.patientId, entity.patientId) &&
                Objects.equals(this.medicalCenterId, entity.medicalCenterId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(patientId, medicalCenterId);
    }

}