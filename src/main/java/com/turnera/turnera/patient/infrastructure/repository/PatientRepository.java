package com.turnera.turnera.patient.infrastructure.repository;

import com.turnera.turnera.patient.infrastructure.entities.Patient;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class PatientRepository implements PanacheRepositoryBase<Patient, Integer> {

  public List<Patient> findAllByIdentificationNumberAndPhone(
      String identificationNumber, String phone) {
    return find("identificationNumber = ?1 AND phone = ?2", identificationNumber, phone).list();
  }

  public Optional<Patient> findByIdentificationNumberAndPhoneAndUserIdIsDefined(
      String identificationNumber, String phone) {
    return find(
            "identificationNumber = ?1 AND phone = ?2 AND user IS NOT NULL",
            identificationNumber,
            phone)
        .firstResultOptional();
  }

  public Patient findByUserIdAndIdentificationNumber(Integer userId, String identificationNumber) {
    return find("user.id = ?1 AND identificationNumber = ?2", userId, identificationNumber)
        .firstResult();
  }

  public long deleteAllByIdIn(List<Integer> ids) {
    return delete("id IN ?1", ids);
  }
}
