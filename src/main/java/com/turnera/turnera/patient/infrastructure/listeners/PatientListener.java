package com.turnera.turnera.patient.infrastructure.listeners;

import com.turnera.turnera.medicalCenter.application.sse.patients.GetPatientDtoForSse;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.sse.application.medicalCenter.SseMedicalCenterEvents;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class PatientListener {

  private final SseMedicalCenterEvents sseMedicalCenterEvents;

  private final GetPatientDtoForSse getPatientDtoForSse;

  @PostPersist
  @PostUpdate
  @PostRemove
  public void afterEvent(Patient patient) {
    Set<PatientMedicalCenterRelationship> relationships =
        patient.getPatientMedicalCenterRelationships();
    if (relationships.isEmpty()) return;
    broadcastPatientUpdate(relationships);
  }

  private void broadcastPatientUpdate(Set<PatientMedicalCenterRelationship> relationships) {
    try {
      for (PatientMedicalCenterRelationship relationship : relationships) {
        Integer medicalCenterId = relationship.getMedicalCenter().getId();
        sseMedicalCenterEvents.broadCastEventCreator(
            () -> getPatientDtoForSse.get(relationship), medicalCenterId);
      }
    } catch (Exception e) {
      log.error("Failed to broadcast patient update SSE event", e);
    }
  }
}
