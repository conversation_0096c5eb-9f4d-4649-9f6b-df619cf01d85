package com.turnera.turnera.patient.infrastructure.listeners;

import com.turnera.turnera.medicalCenter.application.sse.patients.GetPatientDtoForSse;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.sse.application.medicalCenter.SseMedicalCenterEvents;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class PatientMedicalCenterRelationshipListener {

  private final SseMedicalCenterEvents sseMedicalCenterEvents;

  private final GetPatientDtoForSse getPatientDtoForSse;

  @PostPersist
  @PostUpdate
  public void afterUpsert(PatientMedicalCenterRelationship relationship) {
    broadcastPatientUpdate(relationship);
  }

  @PostRemove
  public void afterDelete(PatientMedicalCenterRelationship relationship) {
    broadcastPatientRelationshipDeletion(relationship);
  }

  private void broadcastPatientUpdate(PatientMedicalCenterRelationship relationship) {
    try {
      Integer medicalCenterId = relationship.getMedicalCenter().getId();
      sseMedicalCenterEvents.broadCastEventCreator(
          () -> getPatientDtoForSse.get(relationship), medicalCenterId);
    } catch (Exception e) {
      log.error("Failed to broadcast patient update SSE event", e);
    }
  }

  private void broadcastPatientRelationshipDeletion(PatientMedicalCenterRelationship relationship) {
    try {
      Integer medicalCenterId = relationship.getId().getMedicalCenterId();
      Integer patientId = relationship.getId().getPatientId();
      SseEvent sseEvent =
          new SseEvent("patient_deleted/" + patientId, null, System.currentTimeMillis());
      sseMedicalCenterEvents.broadCastEventCreator(() -> sseEvent, medicalCenterId);
    } catch (Exception e) {
      log.error("Failed to broadcast patient relationship deletion SSE event", e);
    }
  }
}
