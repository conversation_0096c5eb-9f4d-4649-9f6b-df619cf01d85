package com.turnera.turnera.patient.infrastructure.entities;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.infrastructure.listeners.PatientMedicalCenterRelationshipListener;
import com.turnera.turnera.utils.BuenosAiresTime;
import com.turnera.turnera.utils.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "patient_medical_center_relationship")
@EntityListeners(PatientMedicalCenterRelationshipListener.class)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class PatientMedicalCenterRelationship {
  @EqualsAndHashCode.Include @EmbeddedId private PatientMedicalCenterRelationshipId id;

  @MapsId("patientId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "patient_id", nullable = false)
  private Patient patient;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @Column(name = "attendance_percentage", nullable = false)
  private BigDecimal attendancePercentage;

  @Column(name = "appointment_count", nullable = false)
  private Integer appointmentCount;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "creator_type", nullable = false)
  private UserType creatorType;

  @NotNull
  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "updater_type", nullable = false)
  private UserType updaterType;

  @NotNull
  @Column(name = "updated_by", nullable = false)
  private Integer updatedBy;

  @NotNull
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @OneToMany(mappedBy = "patientMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<Appointment> appointments = new LinkedHashSet<>();

  public PatientMedicalCenterRelationship() {}

  public PatientMedicalCenterRelationship(
      PatientMedicalCenterRelationshipId medicalCenterRelationshipId,
      Patient patient,
      MedicalCenter medicalCenter,
      Integer userId,
      UserType creatorType) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    this.id = medicalCenterRelationshipId;
    this.patient = patient;
    this.medicalCenter = medicalCenter;
    this.creatorType = creatorType;
    this.createdBy = userId;
    this.createdAt = now;
    this.updaterType = creatorType;
    this.updatedBy = userId;
    this.updatedAt = now;
  }

  @PrePersist
  protected void onCreate() {
    createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  @PreUpdate
  protected void onUpdate() {
    updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public Integer getCompletedAppointmentCount() {
    return attendancePercentage
        .multiply(BigDecimal.valueOf(appointmentCount.longValue()))
        .intValue();
  }
}
