package com.turnera.turnera.patient.infrastructure.repository;

import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;

@ApplicationScoped
public class PatientMedicalCenterRelationshipRepository
    implements PanacheRepositoryBase<
        PatientMedicalCenterRelationship, PatientMedicalCenterRelationshipId> {

  public List<PatientMedicalCenterRelationship> findAllByPatientIdIn(List<Integer> patientIds) {
    return find("id.patientId IN ?1", patientIds).list();
  }

  public Boolean existsByPatientIdAndMedicalCenterId(Integer patientId, Integer medicalCenterId) {
    return find("id.patientId = ?1 AND id.medicalCenterId = ?2", patientId, medicalCenterId).count()
        > 0;
  }

  @Transactional
  public void migrateRelationshipPatientIdToNewPatientId(
      Integer oldPatientId, Integer newPatientId, Integer medicalCenterId) {
    update(
        "patient.id = ?1 WHERE patient.id = ?2 AND medicalCenter.id = ?3",
        newPatientId,
        oldPatientId,
        medicalCenterId);
  }
}
