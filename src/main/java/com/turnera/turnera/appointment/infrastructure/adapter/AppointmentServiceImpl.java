package com.turnera.turnera.appointment.infrastructure.adapter;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationDetails;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.appointment.infrastructure.entities.AppointmentConsultationTypeRelationship;
import com.turnera.turnera.appointment.infrastructure.repository.AppointmentConsultationTypeRelationshipRepository;
import com.turnera.turnera.appointment.infrastructure.repository.AppointmentRepository;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class AppointmentServiceImpl implements AppointmentService {

  private final AppointmentRepository appointmentRepository;
  private final AppointmentConsultationTypeRelationshipRepository
      appointmentConsultationTypeRelationshipRepository;

  @Override
  public void migrateOldPatientAppointmentsToNewPatientId(
      List<Integer> patientIds, Integer newPatientId) {
    appointmentRepository.migratePatientIdsToNewPatientId(patientIds, newPatientId);
  }

  @Override
  public List<Appointment> findFutureAppointments(
      Integer medicalCenterId, Integer professionalId, LocalDate now) {
    return appointmentRepository
        .findFutureAppointmentsByMedicalCenterIdAndProfessionalIdAndDateIsGreaterThanEqual(
            medicalCenterId, professionalId, now);
  }

  @Override
  public List<Appointment> findAppointmentForDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return appointmentRepository.findAppointmentsByMedicalCenterIdAndProfessionalIdAndDate(
        medicalCenterId, professionalId, date);
  }

  @Override
  public Appointment createAppointment(AppointmentCreationDetails input) {
    Appointment newAppointment = input.toEntity();
    appointmentRepository.persist(newAppointment);
    List<AppointmentConsultationTypeRelationship> relationships =
        input.toRelationshipEntities(newAppointment);
    appointmentConsultationTypeRelationshipRepository.persist(relationships);
    return newAppointment;
  }

  @Override
  public List<Appointment> findAppointmentsForMedicalCenterAndConsultationTypeForDate(
      Integer medicalCenterId, Integer consultationTypeId, LocalDate date) {
    return appointmentRepository.findByMedicalCenterIdAndDateAndConsultationTypeId(
        medicalCenterId, date, consultationTypeId);
  }

  @Override
  public List<Appointment> findAppointmentsByMedicalCenterIdAndProfessionalIdAndDayOfWeek(
      Integer medicalCenterId, Integer professionalId, DayOfWeek dayOfWeek) {
    // DayOfWeek starts at 1 (monday) and ends at 7 (sunday), but in postgresSQL it starts at 0
    // (sunday) and ends at 6 (saturday), sunday becomes 0 because of the remainder
    int dayValue = dayOfWeek.getValue() % 7;
    return appointmentRepository.findByMedicalCenterIdAndProfessionalIdAndDayOfWeek(
        medicalCenterId, professionalId, dayValue);
  }

  @Override
  public List<Appointment> findAppointmentsByMedicalCenterIdAndProfessionalIdAndMonth(
      Integer medicalCenterId, Integer professionalId, java.time.Month month) {
    return appointmentRepository.findByMedicalCenterIdAndProfessionalIdAndMonth(
        medicalCenterId, professionalId, month.getValue());
  }

  @Override
  public Optional<Appointment> findMaybeById(Integer appointmentId) {
    return appointmentRepository.findByIdOptional(appointmentId);
  }

  @Override
  public Appointment saveAppointment(Appointment appointment) {
    appointmentRepository.persist(appointment);
    return appointment;
  }

  @Override
  public Set<Appointment> findAppointmentsByMedicalCenterIdMonthAndYear(
      Integer medicalCenterId, Month month, Year year) {
    return appointmentRepository.findDistinctByMedicalCenterIdMonthAndYear(
        medicalCenterId, month.getValue(), year.getValue());
  }

  @Override
  public void changeAppointmentConsultations(
      Appointment appointment, List<ConsultationType> consultationTypeStream) {
    appointmentConsultationTypeRelationshipRepository.deleteByAppointment(appointment);
    List<AppointmentConsultationTypeRelationship> relationships =
        consultationTypeStream.stream().map(appointment::toConsultationTypeRelationship).toList();
    appointmentConsultationTypeRelationshipRepository.persist(relationships);
  }
}
