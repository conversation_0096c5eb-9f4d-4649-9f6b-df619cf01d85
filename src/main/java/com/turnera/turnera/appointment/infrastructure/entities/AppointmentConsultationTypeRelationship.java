package com.turnera.turnera.appointment.infrastructure.entities;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "appointment_consultation_type_relationship")
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentConsultationTypeRelationship {
  @EmbeddedId private AppointmentConsultationTypeRelationshipId id;

  @MapsId("appointmentId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "appointment_id", nullable = false)
  private Appointment appointment;

  @MapsId("consultationTypeId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "consultation_type_id", nullable = false)
  private ConsultationType consultationType;
}
