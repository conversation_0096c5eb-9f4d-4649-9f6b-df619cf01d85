package com.turnera.turnera.appointment.infrastructure.repository;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.appointment.infrastructure.entities.AppointmentConsultationTypeRelationship;
import com.turnera.turnera.appointment.infrastructure.entities.AppointmentConsultationTypeRelationshipId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class AppointmentConsultationTypeRelationshipRepository
    implements PanacheRepositoryBase<
        AppointmentConsultationTypeRelationship, AppointmentConsultationTypeRelationshipId> {

  public void deleteByAppointment(Appointment appointment) {
    delete("appointment", appointment);
  }
}
