package com.turnera.turnera.appointment.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

@Getter
@Setter
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentConsultationTypeRelationshipId implements Serializable {
  private static final long serialVersionUID = -4188861351226566615L;

  @NotNull
  @Column(name = "appointment_id", nullable = false)
  private Integer appointmentId;

  @NotNull
  @Column(name = "consultation_type_id", nullable = false)
  private Integer consultationTypeId;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    AppointmentConsultationTypeRelationshipId entity =
        (AppointmentConsultationTypeRelationshipId) o;
    return Objects.equals(this.appointmentId, entity.appointmentId)
        && Objects.equals(this.consultationTypeId, entity.consultationTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(appointmentId, consultationTypeId);
  }
}
