package com.turnera.turnera.appointment.domain.entities;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.domain.entities.AppointmentCreationValidationInput;
import com.turnera.turnera.user.infrastructure.entities.User;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentCreationInput {

  private Patient patient;
  private List<ConsultationTypeProfessionalMedicalCenterRelationship>
      consultationTypeProfessionalMedicalCenterRelationships;
  private Optional<Integer> maybeHealthInsuranceId;
  private String patientNotes;
  private Optional<BigDecimal> price;
  private Integer appointmentSlotDuration;
  private LocalDate date;
  private LocalTime startTime;
  private AppointmentSource source;
  private User creator;

  public AppointmentCreationInput(
      Patient patient,
      List<ConsultationTypeProfessionalMedicalCenterRelationship>
          consultationTypeProfessionalMedicalCenterRelationships,
      Optional<Integer> maybeHealthInsuranceId,
      String patientNotes,
      Optional<BigDecimal> price,
      Integer appointmentSlotDuration,
      LocalDate date,
      LocalTime startTime,
      AppointmentSource source,
      User creator) {
    this.patient = patient;
    this.consultationTypeProfessionalMedicalCenterRelationships =
        consultationTypeProfessionalMedicalCenterRelationships;
    this.maybeHealthInsuranceId = maybeHealthInsuranceId;
    this.patientNotes = patientNotes;
    this.price = price;
    this.appointmentSlotDuration = appointmentSlotDuration;
    this.date = date;
    this.startTime = startTime;
    this.source = source;
    this.creator = creator;
  }

  public AppointmentCreationDetails toCreationDetails(Optional<HealthInsurance> healthInsurance) {
    return new AppointmentCreationDetails(
        date,
        startTime,
        appointmentSlotDuration,
        patientNotes,
        patient,
        consultationTypeProfessionalMedicalCenterRelationships.getFirst().getProfessional(),
        consultationTypeProfessionalMedicalCenterRelationships.getFirst().getMedicalCenter(),
        healthInsurance,
        consultationTypeProfessionalMedicalCenterRelationships.stream()
            .map(ConsultationTypeProfessionalMedicalCenterRelationship::getConsultationType)
            .toList(),
        price,
        source,
        creator);
  }

  public AppointmentCreationValidationInput toAppointmentCreationValidationInput(
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship) {
    return new AppointmentCreationValidationInput(
        professionalMedicalCenterRelationship,
        date,
        startTime,
        appointmentSlotDuration,
        creator.isMedicalCenterSide());
  }

  public Integer getMedicalCenterId() {
    return consultationTypeProfessionalMedicalCenterRelationships
        .getFirst()
        .getMedicalCenter()
        .getId();
  }

  public Integer getProfessionalId() {
    return consultationTypeProfessionalMedicalCenterRelationships
        .getFirst()
        .getProfessional()
        .getId();
  }
}
