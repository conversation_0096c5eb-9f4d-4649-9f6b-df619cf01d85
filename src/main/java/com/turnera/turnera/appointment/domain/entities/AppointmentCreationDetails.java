package com.turnera.turnera.appointment.domain.entities;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.appointment.infrastructure.entities.AppointmentConsultationTypeRelationship;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.user.infrastructure.entities.User;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentCreationDetails {

  private LocalDate date;
  private LocalTime startTime;
  private Integer appointmentIntervalAmount;
  private String patientNotes;
  private Patient patient;
  private Professional professional;
  private MedicalCenter medicalCenter;
  private Optional<HealthInsurance> healthInsurance;
  private List<ConsultationType> consultationTypes;
  private Optional<BigDecimal> price;
  private AppointmentSource source;
  private User creator;

  public AppointmentCreationDetails(
      LocalDate date,
      LocalTime startTime,
      Integer appointmentIntervalAmount,
      String patientNotes,
      Patient patient,
      Professional professional,
      MedicalCenter medicalCenter,
      Optional<HealthInsurance> healthInsurance,
      List<ConsultationType> consultationTypes,
      Optional<BigDecimal> price,
      AppointmentSource source,
      User creator) {
    this.date = date;
    this.startTime = startTime;
    this.appointmentIntervalAmount = appointmentIntervalAmount;
    this.patientNotes = patientNotes;
    this.patient = patient;
    this.professional = professional;
    this.medicalCenter = medicalCenter;
    this.healthInsurance = healthInsurance;
    this.consultationTypes = consultationTypes;
    this.price = price;
    this.source = source;
    this.creator = creator;
  }

  public Appointment toEntity() {
    return new Appointment(
        date,
        startTime,
        appointmentIntervalAmount,
        patientNotes,
        patient,
        professional,
        medicalCenter,
        healthInsurance,
        price.orElse(null),
        source,
        creator);
  }

  public List<AppointmentConsultationTypeRelationship> toRelationshipEntities(
      Appointment appointment) {
    return consultationTypes.stream().map(appointment::toConsultationTypeRelationship).toList();
  }
}
