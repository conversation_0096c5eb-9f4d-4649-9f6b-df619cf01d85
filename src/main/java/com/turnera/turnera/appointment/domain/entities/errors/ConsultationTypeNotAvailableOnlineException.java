package com.turnera.turnera.appointment.domain.entities.errors;

import com.turnera.turnera.configuration.exceptions.BadRequestException;

public class ConsultationTypeNotAvailableOnlineException extends BadRequestException {
    public ConsultationTypeNotAvailableOnlineException(Integer consultationTypeId, Integer medicalCenterId, Integer professionalId) {
        super("Consultation type %d is not available online for medical center %d and professional %d".formatted(consultationTypeId, medicalCenterId, professionalId));
    }
}