package com.turnera.turnera.appointment.domain.entities;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AppointmentModificationInput {

  private Optional<Integer> newDoctorId;
  private Appointment appointment;
  private EmployeeUser employeeUser;
  private Optional<LocalDate> newDate;
  private Optional<LocalTime> newStartTime;
  private Optional<List<Integer>> consultationTypeIds;
  private Optional<BigDecimal> newPrice;
  private Optional<AppointmentStatus> newAppointmentStatus;

  public AppointmentModificationInput(
      Appointment appointment,
      EmployeeUser employeeUser,
      Optional<Integer> newDoctorId,
      Optional<LocalDate> newDate,
      Optional<LocalTime> newStartTime,
      Optional<List<Integer>> consultationTypeIds,
      Optional<BigDecimal> newPrice,
      Optional<AppointmentStatus> newAppointmentStatus) {
    this.appointment = appointment;
    this.employeeUser = employeeUser;
    this.newDoctorId = newDoctorId;
    this.newDate = newDate;
    this.newStartTime = newStartTime;
    this.consultationTypeIds = consultationTypeIds;
    this.newPrice = newPrice;
    this.newAppointmentStatus = newAppointmentStatus;
  }
}
