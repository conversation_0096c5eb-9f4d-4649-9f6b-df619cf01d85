package com.turnera.turnera.appointment.presentation.entities.validations;

import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentCreationRequestFromMedicalCenter;
import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentCreationRequestFromUser;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class DateAndStartTimeAfterNowValidator
    implements ConstraintValidator<DateAndStartTimeAfterNow, Object> {

  @Override
  public boolean isValid(Object object, ConstraintValidatorContext context) {
    switch (object) {
      case AppointmentCreationRequestFromUser request -> {
        return validateDateAndTime(request.getDate(), request.getStartTime());
      }
      case AppointmentCreationRequestFromMedicalCenter request -> {
        return validateDateAndTime(request.getDate(), request.getStartTime());
      }
      default -> {
        return true;
      }
    }
  }

  private boolean validateDateAndTime(LocalDate date, LocalTime time) {
    if (date == null || time == null) {
      return true;
    }
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    LocalDateTime appointmentDateTime = LocalDateTime.of(date, time);
    return !now.isAfter(appointmentDateTime);
  }
}
