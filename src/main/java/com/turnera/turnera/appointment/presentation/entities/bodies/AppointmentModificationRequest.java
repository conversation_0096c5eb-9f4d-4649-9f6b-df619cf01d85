package com.turnera.turnera.appointment.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.turnera.turnera.appointment.domain.entities.AppointmentModificationInput;
import com.turnera.turnera.appointment.domain.entities.AppointmentStatus;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.utils.deserializers.CustomDateDeserializer;
import com.turnera.turnera.utils.deserializers.CustomTimeDeserializer;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
public class AppointmentModificationRequest {

  @NotNull private Integer appointmentId;

  @NotNull private Integer employeeUserId;

  private Integer newDoctorId;

  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  @JsonDeserialize(using = CustomDateDeserializer.class)
  private LocalDate newDate;

  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  @JsonDeserialize(using = CustomTimeDeserializer.class)
  private LocalTime newStartTime;

  private List<Integer> consultationTypeIds;

  private BigDecimal newPrice;

  private AppointmentStatus newAppointmentStatus;

  public AppointmentModificationInput toDomain(Appointment appointment, EmployeeUser employeeUser) {
    return new AppointmentModificationInput(
        appointment,
        employeeUser,
        Optional.ofNullable(newDoctorId),
        Optional.ofNullable(newDate),
        Optional.ofNullable(newStartTime),
        Optional.ofNullable(consultationTypeIds),
        Optional.ofNullable(newPrice),
        Optional.ofNullable(newAppointmentStatus));
  }
}
