package com.turnera.turnera.appointment.presentation.entities;

import com.turnera.turnera.consultationType.presentation.entities.ConsultationTypeDTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentDTO {
  private Long id;

  private Integer patientId;

  private String patientName;

  private String identificationNumber;

  private String phone;

  private String email;

  private Integer healthInsuranceId;

  private String healthInsuranceInformation;

  private BigDecimal price;

  private String state;
  private String date;
  private String details;

  private String startTime;

  private int appointmentIntervalAmount;

  private String source;

  private List<ConsultationTypeDTO> consultationTypes;

  private int totalPatientAppointments;

  private BigDecimal attendancePercentage;
}
