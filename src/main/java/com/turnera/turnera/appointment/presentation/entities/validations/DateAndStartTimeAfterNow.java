package com.turnera.turnera.appointment.presentation.entities.validations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateAndStartTimeAfterNowValidator.class)
public @interface DateAndStartTimeAfterNow {
    String message() default "Date and start time must be after the current time";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}