package com.turnera.turnera.appointment.presentation.entities;

import com.turnera.turnera.consultationType.presentation.entities.ConsultationTypeDTO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class ShortenedAppointmentDTO {
  private Long id;

  private LocalDate date;

  private LocalTime startTime;

  private List<ConsultationTypeDTO> consultationTypes;

  private String state;

  private Integer doctorId;

  public LocalDateTime getDateTime() {
    return LocalDateTime.of(date, startTime);
  }
}
