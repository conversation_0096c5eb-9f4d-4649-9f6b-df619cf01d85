package com.turnera.turnera.appointment.application.migrate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.domain.AppointmentService;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class MigrateAppointments {

  private final AppointmentService appointmentService;

  public void migrateOldPatientAppointmentsToNewPatientId(
      List<Integer> patientIds, Integer newPatientId) {
    log.info(
        APPLICATION, "Migrating old patient's appointments to new patient ID: {}", newPatientId);
    appointmentService.migrateOldPatientAppointmentsToNewPatientId(patientIds, newPatientId);
    log.info(
        APPLICATION,
        "Successfully migrated old patient's appointments to new patient ID: {}",
        newPatientId);
  }
}
