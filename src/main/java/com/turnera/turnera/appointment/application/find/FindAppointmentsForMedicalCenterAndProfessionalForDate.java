package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindAppointmentsForMedicalCenterAndProfessionalForDate {

  private final AppointmentService appointmentService;

  public List<Appointment> find(Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return appointmentService.findAppointmentForDate(medicalCenterId, professionalId, date);
  }
}
