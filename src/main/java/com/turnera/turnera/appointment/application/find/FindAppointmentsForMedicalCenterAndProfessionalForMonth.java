package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Month;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindAppointmentsForMedicalCenterAndProfessionalForMonth {

  private final AppointmentService appointmentService;

  public List<Appointment> find(Integer medicalCenterId, Integer professionalId, Month month) {
    log.info(
        "Finding appointments for medical center: {}, professional: {}, month: {}",
        medicalCenterId,
        professionalId,
        month);
    return appointmentService.findAppointmentsByMedicalCenterIdAndProfessionalIdAndMonth(
        medicalCenterId, professionalId, month);
  }
}
