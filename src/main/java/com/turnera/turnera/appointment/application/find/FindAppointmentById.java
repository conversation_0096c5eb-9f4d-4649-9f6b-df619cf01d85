package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindAppointmentById {

  private final AppointmentService appointmentService;

  public Optional<Appointment> findMaybe(Integer appointmentId) {
    log.info("Finding maybe appointment with id {}", appointmentId);
    return appointmentService.findMaybeById(appointmentId);
  }
}
