package com.turnera.turnera.appointment.application.make;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationDetails;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationInput;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.consultationType.application.validations.ConsultationTypeValidations;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.application.find.FindHealthInsurance;
import com.turnera.turnera.healthInsurance.application.find.FindHealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.healthInsurance.application.validations.HealthInsuranceValidations;
import com.turnera.turnera.healthInsurance.domain.errors.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipNotAcceptedError;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.patient.application.update.UpdateMedicalCenterRelationshipByAppointment;
import com.turnera.turnera.patient.application.validations.PatientValidations;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.schedule.application.validations.ValidateAgendaForSlotCreation;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class MakeAppointment {

  private final PatientValidations patientValidations;
  private final AppointmentService appointmentService;
  private final ValidateAgendaForSlotCreation validateAgendaForSlotCreation;
  private final ProfessionalValidations professionalValidations;
  private final HealthInsuranceValidations healthInsuranceValidations;
  private final FindHealthInsurance findHealthInsurance;

  private final UpdateMedicalCenterRelationshipByAppointment
      updateMedicalCenterRelationshipByAppointment;

  private final FindHealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship
      findHealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship;

  private final ConsultationTypeValidations consultationTypeValidations;

  public void make(AppointmentCreationInput input) {
    Integer patientId = input.getPatient().getId();
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    log.info(
        APPLICATION,
        "Creating appointment for patient: {} with professional: {} at medical center: {} on date: {} at time: {}",
        patientId,
        professionalId,
        medicalCenterId,
        input.getDate(),
        input.getStartTime());
    ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship =
        professionalValidations.verifyProfessionalBelongsToMedicalCenter(
            medicalCenterId, professionalId);
    patientValidations.verifyPatientHasArRelationshipWithMedicalCenter(patientId, medicalCenterId);
    if (!input.getCreator().isMedicalCenterSide()) {
      userSideValidation(input, professionalMedicalCenterRelationship);
    }
    Optional<HealthInsurance> maybeHealthInsurance =
        input.getMaybeHealthInsuranceId().map(findHealthInsurance::find);
    AppointmentCreationDetails appointmentCreationDetails =
        input.toCreationDetails(maybeHealthInsurance);
    Appointment appointment = appointmentService.createAppointment(appointmentCreationDetails);
    updateMedicalCenterRelationshipByAppointment.update(appointment);
    log.info(
        APPLICATION,
        "Appointment {} created for patient: {} with professional: {} at medical center: {} on date: {} at time: {}",
        appointment.getId(),
        patientId,
        professionalId,
        medicalCenterId,
        input.getDate(),
        input.getStartTime());
  }

  public void userSideValidation(
      AppointmentCreationInput input,
      ProfessionalMedicalCenterRelationship professionalMedicalCenterRelationship) {
    professionalMedicalCenterRelationship.validateRespectsAnticipationTimeLimits(
        input.getDate(), input.getStartTime());
    consultationTypeValidations.verifyAppointmentRespectsConsultationTypeMedicalCenterRelationship(
        input);
    healthInsuranceValidationForCreation(input);
    validateAgendaForSlotCreation.validate(
        input.toAppointmentCreationValidationInput(professionalMedicalCenterRelationship));
  }

  public void healthInsuranceValidationForCreation(AppointmentCreationInput input) {
    ConsultationTypeProfessionalMedicalCenterRelationship
        consultationTypeProfessionalMedicalCenterRelationship =
            input.getConsultationTypeProfessionalMedicalCenterRelationships().getFirst();
    input
        .getMaybeHealthInsuranceId()
        .ifPresentOrElse(
            healthInsuranceId -> validateHealthInsurance(input, healthInsuranceId),
            consultationTypeProfessionalMedicalCenterRelationship::validateAcceptsSelfPaidPatient);
  }

  private void validateHealthInsurance(AppointmentCreationInput input, Integer healthInsuranceId) {
    Integer medicalCenterId = input.getMedicalCenterId();
    Integer professionalId = input.getProfessionalId();
    Integer consultationTypeId =
        input
            .getConsultationTypeProfessionalMedicalCenterRelationships()
            .getFirst()
            .getConsultationType()
            .getId();
    healthInsuranceValidations.verifyProfessionalMedicalCenterRelationshipAcceptsHealthInsurance(
        healthInsuranceId, medicalCenterId, professionalId);
    Optional<HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship>
        maybeRelationship =
            findHealthInsuranceConsultationTypeProfessionalMedicalCenterRelationship.findMaybe(
                healthInsuranceId, consultationTypeId, medicalCenterId, professionalId);
    maybeRelationship.ifPresent(
        relationship -> {
          if (relationship.getIsExcluded()) {
            throw new HealthInsuranceConsultationTypeProfessionalMedicalCenterRelationshipNotAcceptedError(
                healthInsuranceId, consultationTypeId, medicalCenterId, professionalId);
          }
          input.setPrice(Optional.of(relationship.getCoPaymentPrice()));
        });
  }
}
