package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Month;
import java.time.Year;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindAppointmentsForMedicalCenterInMonthAndYear {

  private final AppointmentService appointmentService;

  public Set<Appointment> find(Integer medicalCenterId, Month month, Year year) {
    log.info(
        "Finding appointments for medical center: {} in month: {} and year: {}",
        medicalCenterId,
        month,
        year);
    return appointmentService.findAppointmentsByMedicalCenterIdMonthAndYear(
        medicalCenterId, month, year);
  }
}
