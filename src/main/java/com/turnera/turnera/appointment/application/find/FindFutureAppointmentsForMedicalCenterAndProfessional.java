package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindFutureAppointmentsForMedicalCenterAndProfessional {

  private final AppointmentService appointmentService;

  public List<Appointment> find(Integer medicalCenterId, Integer professionalId) {
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    return appointmentService.findFutureAppointments(medicalCenterId, professionalId, now);
  }
}
