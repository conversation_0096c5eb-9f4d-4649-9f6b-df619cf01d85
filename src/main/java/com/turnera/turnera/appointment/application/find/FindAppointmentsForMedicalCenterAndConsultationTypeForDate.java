package com.turnera.turnera.appointment.application.find;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindAppointmentsForMedicalCenterAndConsultationTypeForDate {

  private final AppointmentService appointmentService;

  public List<Appointment> find(
      Integer medicalCenterId, Integer consultationTypeId, LocalDate date) {
    return appointmentService.findAppointmentsForMedicalCenterAndConsultationTypeForDate(
        medicalCenterId, consultationTypeId, date);
  }
}
