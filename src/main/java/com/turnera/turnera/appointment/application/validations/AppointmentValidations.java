package com.turnera.turnera.appointment.application.validations;

import com.turnera.turnera.appointment.application.find.FindAppointmentById;
import com.turnera.turnera.appointment.domain.errors.AppointmentNotFoundException;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class AppointmentValidations {

  private final FindAppointmentById findAppointmentById;

  @Transactional
  public Appointment verifyAppointmentExists(Integer appointmentId) {
    log.info("Verifying if appointment with id {} exists", appointmentId);
    Optional<Appointment> maybeAppointment = findAppointmentById.findMaybe(appointmentId);
    if (maybeAppointment.isEmpty()) {
      throw new AppointmentNotFoundException(appointmentId);
    }
    log.info("Appointment with id {} exists", appointmentId);
    return maybeAppointment.get();
  }
}
