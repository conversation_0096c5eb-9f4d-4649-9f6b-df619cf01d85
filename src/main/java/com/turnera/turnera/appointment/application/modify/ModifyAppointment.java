package com.turnera.turnera.appointment.application.modify;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.domain.AppointmentService;
import com.turnera.turnera.appointment.domain.entities.AppointmentModificationInput;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.consultationType.application.validations.ConsultationTypeValidations;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class ModifyAppointment {

  private final AppointmentService appointmentService;

  private final ProfessionalValidations professionalValidations;

  private final ConsultationTypeValidations consultationTypeValidations;

  @Transactional
  public void modify(AppointmentModificationInput input) {
    Appointment appointment = input.getAppointment();
    log.info(APPLICATION, "Modifying appointment with ID: {}", appointment.getId());
    updateAppointmentDetails(input, appointment);
    Appointment updatedAppointment = appointmentService.saveAppointment(appointment);
    log.info(
        APPLICATION, "Successfully modified appointment with ID: {}", updatedAppointment.getId());
  }

  private void updateAppointmentDetails(
      AppointmentModificationInput input, Appointment appointment) {
    Integer medicalCenterId = appointment.getMedicalCenter().getId();
    updateDoctor(input, appointment, medicalCenterId);
    updateConsultationTypes(input, appointment, medicalCenterId);
    updateDate(input, appointment);
    updateStartTime(input, appointment);
    updatePrice(input, appointment);
    updateState(input, appointment);
  }

  private void updateDoctor(
      AppointmentModificationInput input, Appointment appointment, Integer medicalCenterId) {
    input
        .getNewDoctorId()
        .ifPresent(
            newDoctorId -> {
              log.info(
                  APPLICATION,
                  "Validating new doctor with ID: {} for medical center: {}",
                  newDoctorId,
                  medicalCenterId);
              ProfessionalMedicalCenterRelationship professionalRelationship =
                  professionalValidations.verifyProfessionalBelongsToMedicalCenter(
                      medicalCenterId, newDoctorId);
              appointment.setProfessional(professionalRelationship.getProfessional());
              log.info(APPLICATION, "Updated appointment doctor to ID: {}", newDoctorId);
            });
  }

  private void updateConsultationTypes(
      AppointmentModificationInput input, Appointment appointment, Integer medicalCenterId) {
    input
        .getConsultationTypeIds()
        .ifPresent(
            consultationTypeIds -> {
              log.info(
                  APPLICATION,
                  "Validating consultation type with IDS: {} for medical center: {} and professional: {}",
                  consultationTypeIds,
                  medicalCenterId,
                  appointment.getProfessional().getId());
              List<ConsultationTypeProfessionalMedicalCenterRelationship>
                  consultationTypeRelationships =
                      consultationTypeValidations
                          .verifyProfessionalMedicalCenterConsultationTypeRelationshipsExists(
                              consultationTypeIds,
                              medicalCenterId,
                              appointment.getProfessional().getId());
              appointmentService.changeAppointmentConsultations(
                  appointment,
                  consultationTypeRelationships.stream()
                      .map(
                          ConsultationTypeProfessionalMedicalCenterRelationship
                              ::getConsultationType)
                      .toList());
              log.info(
                  APPLICATION,
                  "Updated appointment consultation type to IDS: {}",
                  consultationTypeIds);
            });
  }

  private void updateDate(AppointmentModificationInput input, Appointment appointment) {
    input
        .getNewDate()
        .ifPresent(
            newDate -> {
              log.info(APPLICATION, "Updating appointment date to: {}", newDate);
              appointment.setDate(newDate);
            });
  }

  private void updateStartTime(AppointmentModificationInput input, Appointment appointment) {
    input
        .getNewStartTime()
        .ifPresent(
            newStartTime -> {
              log.info(APPLICATION, "Updating appointment start time to: {}", newStartTime);
              appointment.setStartTime(newStartTime);
            });
  }

  private void updatePrice(AppointmentModificationInput input, Appointment appointment) {
    input
        .getNewPrice()
        .ifPresent(
            newPrice -> {
              log.info(APPLICATION, "Updating appointment price to: {}", newPrice);
              appointment.setPrice(newPrice);
            });
  }

  private void updateState(AppointmentModificationInput input, Appointment appointment) {
    input
        .getNewAppointmentStatus()
        .ifPresent(
            newStatus -> {
              log.info(APPLICATION, "Updating appointment status to: {}", newStatus);
              appointment.setStatus(newStatus);
            });
  }
}
