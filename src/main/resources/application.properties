quarkus.application.name=turnera
quarkus.swagger-ui.path=/docs
quarkus.smallrye-openapi.path=/api-docs
quarkus.swagger-ui.always-include=true
quarkus.smallrye-health.ui.enable=true
quarkus.info.build.version=@project.version@
quarkus.jackson.fail-on-unknown-properties=true
quarkus.datasource.db-kind=postgresql
quarkus.datasource.jdbc.url=jdbc:postgresql://${DB_HOST:localhost}/${DB_NAME:turnera}
quarkus.datasource.username=${DB_USER:postgres}
quarkus.datasource.password=${DB_PASSWORD:password}
quarkus.hibernate-orm.database.generation=validate
quarkus.hibernate-orm.log.sql=false
quarkus.http.proxy.proxy-address-forwarding=true
quarkus.http.proxy.allow-forwarded=true
quarkus.log.level=INFO
quarkus.log.category."com.turnera".level=DEBUG
quarkus.http.record-request-start-time=true