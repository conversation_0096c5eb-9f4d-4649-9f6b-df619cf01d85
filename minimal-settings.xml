<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
  
  <!-- Optional: Define local repository location if you want to keep it -->
  <localRepository>${user.home}/.m2/repository</localRepository>
  
  <!-- No mirrors defined - will use Maven Central directly -->
  
  <!-- No custom servers defined -->
  
  <!-- No custom profiles defined -->
  
</settings>
